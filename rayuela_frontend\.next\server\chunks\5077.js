"use strict";exports.id=5077,exports.ids=[5077],exports.modules={5077:(e,t,r)=>{let n;r.d(t,{Ay:()=>en});var i=r(43210),a=r(57379),u=Object.prototype.hasOwnProperty;let o=new WeakMap,l=()=>{},s=l(),c=Object,d=e=>e===s,f=e=>"function"==typeof e,g=(e,t)=>({...e,...t}),h=e=>f(e.then),p={},y={},v="undefined",b=typeof document!=v,m=!1,w=()=>!1,O=(e,t)=>{let r=o.get(e);return[()=>!d(t)&&e.get(t)||p,n=>{if(!d(t)){let i=e.get(t);t in y||(y[t]=i),r[5](t,g(i,n),i||p)}},r[6],()=>!d(t)&&t in y?y[t]:!d(t)&&e.get(t)||p]},S=!0,[E,R]=[l,l],_={initFocus:e=>(b&&document.addEventListener("visibilitychange",e),E("focus",e),()=>{b&&document.removeEventListener("visibilitychange",e),R("focus",e)}),initReconnect:e=>{let t=()=>{S=!0,e()},r=()=>{S=!1};return E("online",t),E("offline",r),()=>{R("online",t),R("offline",r)}}},k=!i.useId,T=!0,L=e=>w()?window.requestAnimationFrame(e):setTimeout(e,1),V=T?i.useEffect:i.useLayoutEffect,j="undefined"!=typeof navigator&&navigator.connection,x=!T&&j&&(["slow-2g","2g"].includes(j.effectiveType)||j.saveData),C=new WeakMap,D=(e,t)=>c.prototype.toString.call(e)===`[object ${t}]`,P=0,I=e=>{let t,r,n=typeof e,i=D(e,"Date"),a=D(e,"RegExp"),u=D(e,"Object");if(c(e)!==e||i||a)t=i?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=C.get(e))return t;if(t=++P+"~",C.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=I(e[r])+",";C.set(e,t)}if(u){t="#";let n=c.keys(e).sort();for(;!d(r=n.pop());)d(e[r])||(t+=r+":"+I(e[r])+",");C.set(e,t)}}return t},A=e=>{if(f(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?I(e):"",t]},F=0,M=()=>++F;async function W(...e){let[t,r,n,i]=e,a=g({populateCache:!0,throwOnError:!0},"boolean"==typeof i?{revalidate:i}:i||{}),u=a.populateCache,l=a.rollbackOnError,c=a.optimisticData,p=e=>"function"==typeof l?l(e):!1!==l,y=a.throwOnError;if(f(r)){let e=[];for(let n of t.keys())!/^\$(inf|sub)\$/.test(n)&&r(t.get(n)._k)&&e.push(n);return Promise.all(e.map(v))}return v(r);async function v(r){let i,[l]=A(r);if(!l)return;let[g,v]=O(t,l),[b,m,w,S]=o.get(t),E=()=>{let e=b[l];return(f(a.revalidate)?a.revalidate(g().data,r):!1!==a.revalidate)&&(delete w[l],delete S[l],e&&e[0])?e[0](2).then(()=>g().data):g().data};if(e.length<3)return E();let R=n,_=M();m[l]=[_,0];let k=!d(c),T=g(),L=T.data,V=T._c,j=d(V)?L:V;if(k&&v({data:c=f(c)?c(j,L):c,_c:j}),f(R))try{R=R(j)}catch(e){i=e}if(R&&h(R)){if(R=await R.catch(e=>{i=e}),_!==m[l][0]){if(i)throw i;return R}i&&k&&p(i)&&(u=!0,v({data:j,_c:s}))}if(u&&!i&&(f(u)?v({data:u(R,j),error:s,_c:s}):v({data:R,error:s,_c:s})),m[l][1]=M(),Promise.resolve(E()).then(()=>{v({_c:s})}),i){if(y)throw i;return}return R}}let $=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},q=(e,t)=>{if(!o.has(e)){let r=g(_,t),n=Object.create(null),i=W.bind(s,e),a=l,u=Object.create(null),c=(e,t)=>{let r=u[e]||[];return u[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},d=(t,r,n)=>{e.set(t,r);let i=u[t];if(i)for(let e of i)e(r,n)},f=()=>{if(!o.has(e)&&(o.set(e,[n,Object.create(null),Object.create(null),Object.create(null),i,d,c]),!T)){let t=r.initFocus(setTimeout.bind(s,$.bind(s,n,0))),i=r.initReconnect(setTimeout.bind(s,$.bind(s,n,1)));a=()=>{t&&t(),i&&i(),o.delete(e)}}};return f(),[e,i,f,a]}return[e,o.get(e)[4]]},[J,N]=q(new Map),U=g({onLoadingSlow:l,onSuccess:l,onError:l,onErrorRetry:(e,t,r,n,i)=>{let a=r.errorRetryCount,u=i.retryCount,o=~~((Math.random()+.5)*(1<<(u<8?u:8)))*r.errorRetryInterval;(d(a)||!(u>a))&&setTimeout(n,o,i)},onDiscarded:l,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:x?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:x?5e3:3e3,compare:function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!n||"object"==typeof t){for(n in i=0,t)if(u.call(t,n)&&++i&&!u.call(r,n)||!(n in r)||!e(t[n],r[n]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r},isPaused:()=>!1,cache:J,mutate:N,fallback:{}},{isOnline:()=>S,isVisible:()=>{let e=b&&document.visibilityState;return d(e)||"hidden"!==e}}),z=(e,t)=>{let r=g(e,t);if(t){let{use:n,fallback:i}=e,{use:a,fallback:u}=t;n&&a&&(r.use=n.concat(a)),i&&u&&(r.fallback=g(i,u))}return r},B=(0,i.createContext)({}),G=!1,H=G?window.__SWR_DEVTOOLS_USE__:[],K=e=>f(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],Q=()=>g(U,(0,i.useContext)(B)),X=H.concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=A(t),[,,,i]=o.get(J);if(n.startsWith("$inf$"))return r(...e);let a=i[n];return d(a)?r(...e):(delete i[n],a)});return e(t,i,n)}),Y=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}};G&&(window.__SWR_DEVTOOLS_REACT__=i);let Z=()=>{},ee=Z();new WeakMap;let et=i.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),er={dedupe:!0};c.defineProperty(e=>{let{value:t}=e,r=(0,i.useContext)(B),n=f(t),a=(0,i.useMemo)(()=>n?t(r):t,[n,r,t]),u=(0,i.useMemo)(()=>n?a:z(r,a),[n,r,a]),o=a&&a.provider,l=(0,i.useRef)(s);o&&!l.current&&(l.current=q(o(u.cache||J),a));let c=l.current;return c&&(u.cache=c[0],u.mutate=c[1]),V(()=>{if(c)return c[2]&&c[2](),c[3]},[]),(0,i.createElement)(B.Provider,g(e,{value:u}))},"defaultValue",{value:U});let en=(n=(e,t,r)=>{let{cache:n,compare:u,suspense:l,fallbackData:c,revalidateOnMount:p,revalidateIfStale:y,refreshInterval:v,refreshWhenHidden:b,refreshWhenOffline:m,keepPreviousData:w}=r,[S,E,R,_]=o.get(n),[j,x]=A(e),C=(0,i.useRef)(!1),D=(0,i.useRef)(!1),P=(0,i.useRef)(j),I=(0,i.useRef)(t),F=(0,i.useRef)(r),$=()=>F.current,q=()=>$().isVisible()&&$().isOnline(),[J,N,U,z]=O(n,j),B=(0,i.useRef)({}).current,G=d(c)?d(r.fallback)?s:r.fallback[j]:c,H=(e,t)=>{for(let r in B)if("data"===r){if(!u(e[r],t[r])&&(!d(e[r])||!u(eu,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},K=(0,i.useMemo)(()=>{let e=!!j&&!!t&&(d(p)?!$().isPaused()&&!l&&!1!==y:p),r=t=>{let r=g(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=J(),i=z(),a=r(n),u=n===i?a:r(i),o=a;return[()=>{let e=r(J());return H(e,o)?(o.data=e.data,o.isLoading=e.isLoading,o.isValidating=e.isValidating,o.error=e.error,o):(o=e,e)},()=>u]},[n,j]),Q=(0,a.useSyncExternalStore)((0,i.useCallback)(e=>U(j,(t,r)=>{H(r,t)||e()}),[n,j]),K[0],K[1]),X=!C.current,Z=S[j]&&S[j].length>0,ee=Q.data,en=d(ee)?G&&h(G)?et(G):G:ee,ei=Q.error,ea=(0,i.useRef)(en),eu=w?d(ee)?d(ea.current)?en:ea.current:ee:en,eo=(!Z||!!d(ei))&&(X&&!d(p)?p:!$().isPaused()&&(l?!d(en)&&y:d(en)||y)),el=!!(j&&t&&X&&eo),es=d(Q.isValidating)?el:Q.isValidating,ec=d(Q.isLoading)?el:Q.isLoading,ed=(0,i.useCallback)(async e=>{let t,n,i=I.current;if(!j||!i||D.current||$().isPaused())return!1;let a=!0,o=e||{},l=!R[j]||!o.dedupe,c=()=>k?!D.current&&j===P.current&&C.current:j===P.current,g={isValidating:!1,isLoading:!1},h=()=>{N(g)},p=()=>{let e=R[j];e&&e[1]===n&&delete R[j]},y={isValidating:!0};d(J().data)&&(y.isLoading=!0);try{if(l&&(N(y),r.loadingTimeout&&d(J().data)&&setTimeout(()=>{a&&c()&&$().onLoadingSlow(j,r)},r.loadingTimeout),R[j]=[i(x),M()]),[t,n]=R[j],t=await t,l&&setTimeout(p,r.dedupingInterval),!R[j]||R[j][1]!==n)return l&&c()&&$().onDiscarded(j),!1;g.error=s;let e=E[j];if(!d(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return h(),l&&c()&&$().onDiscarded(j),!1;let o=J().data;g.data=u(o,t)?o:t,l&&c()&&$().onSuccess(t,j,r)}catch(r){p();let e=$(),{shouldRetryOnError:t}=e;!e.isPaused()&&(g.error=r,l&&c()&&(e.onError(r,j,e),(!0===t||f(t)&&t(r))&&(!$().revalidateOnFocus||!$().revalidateOnReconnect||q())&&e.onErrorRetry(r,j,e,e=>{let t=S[j];t&&t[0]&&t[0](3,e)},{retryCount:(o.retryCount||0)+1,dedupe:!0})))}return a=!1,h(),!0},[j,n]),ef=(0,i.useCallback)((...e)=>W(n,P.current,...e),[]);if(V(()=>{I.current=t,F.current=r,d(ee)||(ea.current=ee)}),V(()=>{if(!j)return;let e=ed.bind(s,er),t=0;$().revalidateOnFocus&&(t=Date.now()+$().focusThrottleInterval);let r=Y(j,S,(r,n={})=>{if(0==r){let r=Date.now();$().revalidateOnFocus&&r>t&&q()&&(t=r+$().focusThrottleInterval,e())}else if(1==r)$().revalidateOnReconnect&&q()&&e();else if(2==r)return ed();else if(3==r)return ed(n)});return D.current=!1,P.current=j,C.current=!0,N({_k:x}),eo&&(d(en)||T?e():L(e)),()=>{D.current=!0,r()}},[j]),V(()=>{let e;function t(){let t=f(v)?v(J().data):v;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!J().error&&(b||$().isVisible())&&(m||$().isOnline())?ed(er).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[v,b,m,j]),(0,i.useDebugValue)(eu),l&&d(en)&&j){if(!k&&T)throw Error("Fallback data is required when using Suspense in SSR.");I.current=t,F.current=r,D.current=!1;let e=_[j];if(d(e)||et(ef(e)),d(ei)){let e=ed(er);d(eu)||(e.status="fulfilled",e.value=!0),et(e)}else throw ei}return{mutate:ef,get data(){return B.data=!0,eu},get error(){return B.error=!0,ei},get isValidating(){return B.isValidating=!0,es},get isLoading(){return B.isLoading=!0,ec}}},function(...e){let t=Q(),[r,i,a]=K(e),u=z(t,a),o=n,{use:l}=u,s=(l||[]).concat(X);for(let e=s.length;e--;)o=s[e](o);return o(r,i||u.fetcher||null,u)})},53332:(e,t,r)=>{var n=r(43210),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,u=n.useEffect,o=n.useLayoutEffect,l=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return o(function(){i.value=r,i.getSnapshot=t,s(i)&&c({inst:i})},[e,r,t]),u(function(){return s(i)&&c({inst:i}),e(function(){s(i)&&c({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},57379:(e,t,r)=>{e.exports=r(53332)}};