import { useState, useEffect } from 'react';
import { getRayuela, type BatchIngestionJobStatus, type BatchIngestionRequest } from '@/lib/generated/rayuelaAPI';

// Extend the generated schema with local-only fields (derived values, UI helpers)
export type IngestionJob = BatchIngestionJobStatus & {
  /** Derived duration in seconds – not provided by the backend */
  duration?: number;
  /** Optional file metadata when available (extension field not in OpenAPI yet) */
  filePath?: string;
  fileSize?: number;
  /** Flattened counts for quick access */
  records_processed?: {
    users?: number;
    products?: number;
    interactions?: number;
    total?: number;
  };
};

export function useIngestionJobs() {
  const [jobs, setJobs] = useState<IngestionJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchJobs = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch recent batch ingestion jobs via the backend listing endpoint
      try {
        const response = await getRayuela().listBatchJobsApiV1IngestionBatchGet();
        const jobsList = response.data;

        // Transform the backend response to match our interface
        const jobsData: IngestionJob[] = jobsList.map((apiJobStatus) => {
          const job: IngestionJob = {
            ...apiJobStatus,
            status: apiJobStatus.status.toUpperCase() as 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED',
            // Derived/flattened helpers
            records_processed: apiJobStatus.processedCount ? {
              users: (apiJobStatus.processedCount as Record<string, number>).users,
              products: (apiJobStatus.processedCount as Record<string, number>).products,
              interactions: (apiJobStatus.processedCount as Record<string, number>).interactions,
              total: (apiJobStatus.processedCount as Record<string, number>).total,
            } : undefined,
          };
          
          // Calculate duration if both startedAt and completedAt are available
          if (job.startedAt && job.completedAt) {
            const startTime = new Date(job.startedAt).getTime();
            const endTime = new Date(job.completedAt).getTime();
            job.duration = Math.round((endTime - startTime) / 1000); // in seconds
          }
          
          return job;
        });
        
        setJobs(jobsData);
        return; // Exit early if successful
      } catch (listingError) {
        setError('Error fetching ingestion jobs');
        console.error('Error fetching ingestion jobs:', listingError);
      }
      
      // No fallback: if listing fails, error is set and UI can react accordingly
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading ingestion jobs');
      console.error('Error loading ingestion jobs:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getJobStatus = async (jobId: number) => {
    try {
      const response = await getRayuela().getBatchJobStatusApiV1IngestionBatchJobIdGet(jobId);
      return response.data;
    } catch (err) {
      console.error('Error fetching job status:', err);
      throw err;
    }
  };

  const startBatchIngestion = async (data: Record<string, unknown>) => {
    try {
      const response = await getRayuela().batchDataIngestionApiV1IngestionBatchPost(data);
      
      // Refresh jobs list directly; backend will include the new job
      await fetchJobs();
      return response.data;
    } catch (err) {
      console.error('Error starting batch ingestion:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  return {
    jobs,
    isLoading,
    error,
    fetchJobs,
    getJobStatus,
    startBatchIngestion
  };
} 