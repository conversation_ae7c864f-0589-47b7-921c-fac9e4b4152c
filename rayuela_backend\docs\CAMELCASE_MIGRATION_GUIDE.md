# Guía de Migración a camelCase para API de Rayuela

## Resumen

A partir de la versión actual, la API de Rayuela utiliza nomenclatura **camelCase** consistente en todos los niveles de los payloads JSON. Esta guía explica los cambios y cómo migrar tu código existente.

## ¿Qué cambió?

### Antes (Inconsistente)
```json
{
  "users": [  // snake_case esperado en nivel superior
    {
      "externalId": "user_001",  // camelCase esperado en campos anidados
      "preferredCategories": ["electronics"]
    }
  ]
}
```

### Después (Consistente)
```json
{
  "users": [  // camelCase consistente en todos los niveles
    {
      "externalId": "user_001",
      "preferredCategories": ["electronics"],
      "dislikedCategories": ["sports"],
      "priceRangeMin": 10.50,
      "priceRangeMax": 500.00,
      "demographicInfo": {
        "age": 28,
        "gender": "M"
      }
    }
  ]
}
```

## Campos Afectados

### Usuarios (EndUser)
| Campo Anterior | Campo Nuevo | Tipo |
|----------------|-------------|------|
| `external_id` | `externalId` | string |
| `preferred_categories` | `preferredCategories` | string[] |
| `disliked_categories` | `dislikedCategories` | string[] |
| `preferred_brands` | `preferredBrands` | string[] |
| `disliked_brands` | `dislikedBrands` | string[] |
| `price_range_min` | `priceRangeMin` | number |
| `price_range_max` | `priceRangeMax` | number |
| `demographic_info` | `demographicInfo` | object |
| `onboarding_preferences` | `onboardingPreferences` | object |

### Productos (Product)
| Campo Anterior | Campo Nuevo | Tipo |
|----------------|-------------|------|
| `external_id` | `externalId` | string |
| `average_rating` | `averageRating` | number |
| `num_ratings` | `numRatings` | number |
| `inventory_count` | `inventoryCount` | number |

### Interacciones (Interaction)
| Campo Anterior | Campo Nuevo | Tipo |
|----------------|-------------|------|
| `user_id` | `userId` | number |
| `product_id` | `productId` | number |
| `interaction_type` | `interactionType` | string |
| `recommendation_metadata` | `recommendationMetadata` | object |

## Compatibilidad

### ✅ Retrocompatibilidad
La API **mantiene compatibilidad** con `snake_case` para facilitar la migración:

```json
// ✅ Esto sigue funcionando
{
  "users": [
    {
      "external_id": "user_001",
      "preferred_categories": ["electronics"]
    }
  ]
}

// ✅ Esto también funciona (recomendado)
{
  "users": [
    {
      "externalId": "user_001",
      "preferredCategories": ["electronics"]
    }
  ]
}
```

### 📤 Respuestas de la API
Las respuestas de la API **siempre** usan `camelCase`:

```json
{
  "jobId": 123,
  "status": "COMPLETED",
  "totalUsers": 100,
  "totalProducts": 50,
  "totalInteractions": 200
}
```

## Migración Paso a Paso

### 1. Actualizar Tipos TypeScript (Frontend)
Si usas el cliente generado de Orval:

```bash
npm run generate-api
```

Los tipos se actualizarán automáticamente para usar `camelCase`.

### 2. Actualizar Código de Cliente

#### JavaScript/TypeScript
```javascript
// ❌ Antes
const data = {
  users: [
    {
      external_id: "user_001",
      preferred_categories: ["electronics"]
    }
  ]
};

// ✅ Después
const data = {
  users: [
    {
      externalId: "user_001",
      preferredCategories: ["electronics"]
    }
  ]
};
```

#### Python
```python
# ❌ Antes
data = {
    "users": [
        {
            "external_id": "user_001",
            "preferred_categories": ["electronics"]
        }
    ]
}

# ✅ Después (recomendado)
data = {
    "users": [
        {
            "externalId": "user_001",
            "preferredCategories": ["electronics"]
        }
    ]
}

# ✅ También funciona (retrocompatibilidad)
# El formato anterior sigue siendo válido
```

### 3. Actualizar Archivos CSV

Si usas archivos CSV, puedes usar tanto `snake_case` como `camelCase` en los headers:

```csv
# ✅ Ambos formatos son válidos
externalId,preferredCategories,priceRangeMin
user_001,"electronics,books",10.50

# o

external_id,preferred_categories,price_range_min
user_001,"electronics,books",10.50
```

## Ejemplos Actualizados

### Ingesta Masiva Completa
```json
{
  "users": [
    {
      "externalId": "user_001",
      "preferredCategories": ["electronics", "books"],
      "dislikedCategories": ["sports"],
      "priceRangeMin": 10.50,
      "priceRangeMax": 500.00,
      "demographicInfo": {
        "age": 28,
        "gender": "M",
        "location": "urban"
      },
      "onboardingPreferences": {
        "shoppingFrequency": "weekly",
        "interests": ["technology", "fashion"]
      }
    }
  ],
  "products": [
    {
      "externalId": "prod_001",
      "name": "Smartphone XYZ",
      "description": "Un smartphone de última generación",
      "price": 599.99,
      "category": "electronics",
      "averageRating": 4.5,
      "numRatings": 120,
      "inventoryCount": 50
    }
  ],
  "interactions": [
    {
      "userId": 1,
      "productId": 1,
      "interactionType": "VIEW",
      "value": 1.0,
      "recommendationMetadata": {
        "source": "homepage",
        "algorithm": "collaborative_filtering",
        "confidence": 0.85
      }
    }
  ]
}
```

## Herramientas de Migración

### Script de Conversión (JavaScript)
```javascript
function convertToCase(obj, toCamelCase = true) {
  if (Array.isArray(obj)) {
    return obj.map(item => convertToCase(item, toCamelCase));
  }
  
  if (obj !== null && typeof obj === 'object') {
    const converted = {};
    for (const [key, value] of Object.entries(obj)) {
      const newKey = toCamelCase ? 
        key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) :
        key.replace(/([A-Z])/g, '_$1').toLowerCase();
      converted[newKey] = convertToCase(value, toCamelCase);
    }
    return converted;
  }
  
  return obj;
}

// Uso
const snakeCaseData = { external_id: "user_001", preferred_categories: ["electronics"] };
const camelCaseData = convertToCase(snakeCaseData, true);
console.log(camelCaseData); // { externalId: "user_001", preferredCategories: ["electronics"] }
```

## Preguntas Frecuentes

### ¿Debo migrar inmediatamente?
No es urgente. La API mantiene retrocompatibilidad con `snake_case`, pero recomendamos migrar gradualmente para consistencia.

### ¿Las respuestas de la API cambiaron?
Sí, las respuestas ahora usan `camelCase` consistentemente. Si tu código parsea respuestas, verifica que maneje los nuevos nombres de campos.

### ¿Qué pasa con mis archivos CSV existentes?
Siguen funcionando. El sistema acepta tanto `snake_case` como `camelCase` en los headers de CSV.

### ¿Hay algún endpoint que no haya cambiado?
Todos los endpoints que manejan datos de usuarios, productos e interacciones ahora usan `camelCase` consistente.

## Soporte

Si tienes problemas durante la migración:
- Revisa los logs de la API para errores de validación específicos
- Usa el endpoint `/health/auth` para verificar que tu API key funciona
- Contacta soporte técnico en [<EMAIL>](mailto:<EMAIL>)

## Changelog

- **2025-07-12**: Implementación de camelCase consistente en todos los esquemas de ingesta masiva
- **2025-07-12**: Actualización de documentación y ejemplos
- **2025-07-12**: Mantenimiento de retrocompatibilidad con snake_case
