# 6. Recomendaciones

Esta sección detalla los endpoints disponibles para obtener recomendaciones personalizadas y no personalizadas en Rayuela.

## Visión General

<PERSON><PERSON><PERSON> ofrece varios tipos de recomendaciones para adaptarse a diferentes casos de uso:

- **Recomendaciones Personalizadas**: Adaptadas a un usuario específico basándose en su historial de interacciones
- **Productos Similares**: Basados en similitud de contenido con un producto específico
- **Recomendaciones Populares**: Basadas en tendencias generales, ventas o valoraciones
- **Recomendaciones Contextuales**: Adaptadas al contexto actual (página, dispositivo, etc.)

## Recomendaciones Personalizadas (`POST /recommendations/personalized/query`)

Este es el endpoint principal y recomendado para obtener recomendaciones personalizadas. Permite especificar filtros estructurados complejos y contexto en el cuerpo de la solicitud, facilitando la construcción de consultas avanzadas que no serían posibles con parámetros de consulta.

### Request Body

El cuerpo de la solicitud debe seguir el esquema `RecommendationQueryRequest`:

### Usando ID Interno (user_id)

```json
{
  "user_id": 123,
  "filters": {
    "logic": "and",
    "filters": [
      {
        "field": "price",
        "operator": "lt",
        "value": 50.0
      },
      {
        "logic": "or",
        "filters": [
          {
            "field": "category",
            "operator": "eq",
            "value": "electronics"
          },
          {
            "field": "brand",
            "operator": "in",
            "value": ["Samsung", "Apple"]
          }
        ]
      }
    ]
  },
  "context": {
    "page_type": "product_detail",
    "device": "mobile",
    "source_item_id": 456,
    "recently_viewed_ids": [789, 1011]
  },
  "strategy": "balanced",
  "model_type": "standard",
  "include_explanation": true,
  "skip": 0,
  "limit": 10
}
```

### Usando ID Externo (external_user_id) - Recomendado

```json
{
  "external_user_id": "user_abc123",
  "filters": {
    "logic": "and",
    "filters": [
      {
        "field": "price",
        "operator": "lt",
        "value": 50.0
      },
      {
        "logic": "or",
        "filters": [
          {
            "field": "category",
            "operator": "eq",
            "value": "electronics"
          },
          {
            "field": "brand",
            "operator": "in",
            "value": ["Samsung", "Apple"]
          }
        ]
      }
    ]
  },
  "context": {
    "page_type": "product_detail",
    "device": "mobile",
    "source_external_product_id": "prod_xyz789",
    "recently_viewed_ids": [789, 1011]
  },
  "strategy": "balanced",
  "model_type": "standard",
  "include_explanation": true,
  "skip": 0,
  "limit": 10
}
```

#### Parámetros

- **`user_id`** (obligatorio): ID del usuario para el que se generan recomendaciones
- **`filters`** (opcional): Filtros estructurados para aplicar a las recomendaciones
- **`context`** (opcional): Contexto explícito para filtrar o re-rankear recomendaciones
- **`strategy`** (opcional): Estrategia de recomendación a utilizar
- **`model_type`** (opcional, default: "standard"): Tipo de modelo a utilizar
- **`include_explanation`** (opcional, default: false): Incluir explicación de la recomendación
- **`sort_by`** (opcional): Configuración de ordenamiento
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Filtros Estructurados

Los filtros permiten refinar las recomendaciones según criterios específicos. Pueden ser simples o complejos con lógica anidada:

- **Filtro Simple**: Especifica un campo, un operador y un valor
  ```json
  {
    "field": "price",
    "operator": "lt",
    "value": 50.0
  }
  ```

- **Filtro Compuesto**: Combina múltiples filtros con operadores lógicos
  ```json
  {
    "logic": "and",
    "filters": [
      { "field": "category", "operator": "eq", "value": "electronics" },
      { "field": "price", "operator": "lt", "value": 100.0 }
    ]
  }
  ```

- **Operadores de Filtro**:
  - `eq`: Igual a
  - `ne`: No igual a
  - `gt`: Mayor que
  - `gte`: Mayor o igual que
  - `lt`: Menor que
  - `lte`: Menor o igual que
  - `in`: Valor en lista
  - `nin`: Valor no en lista
  - `contains`: Contiene substring (para texto)
  - `startswith`: Comienza con (para texto)
  - `endswith`: Termina con (para texto)

- **Operadores Lógicos**:
  - `and`: Todos los filtros deben cumplirse
  - `or`: Al menos un filtro debe cumplirse

#### Contexto de Recomendación

El contexto proporciona información adicional para mejorar la relevancia de las recomendaciones:

```json
{
  "page_type": "product_detail",
  "device": "mobile",
  "source_item_id": 456,
  "search_query": "smartphone",
  "category_id": 10,
  "cart_item_ids": [123, 456],
  "recently_viewed_ids": [789, 1011],
  "location": "Buenos Aires",
  "time_of_day": "evening",
  "custom_attributes": {
    "season": "summer",
    "promotion_active": true
  }
}
```

- **`page_type`**: Tipo de página donde se muestran las recomendaciones (product_detail, category, search, cart, home)
- **`device`**: Tipo de dispositivo (desktop, mobile, tablet)
- **`source_item_id`**: ID del producto de origen (útil en páginas de detalle de producto)
- **`search_query`**: Consulta de búsqueda actual
- **`category_id`**: ID de la categoría actual
- **`cart_item_ids`**: IDs de productos en el carrito
- **`recently_viewed_ids`**: IDs de productos vistos recientemente
- **`location`**: Ubicación geográfica del usuario
- **`time_of_day`**: Momento del día (morning, afternoon, evening, night)
- **`custom_attributes`**: Atributos personalizados adicionales

#### Estrategias de Recomendación

Rayuela ofrece varias estrategias predefinidas para diferentes objetivos de negocio:

- **`balanced`**: Equilibrio entre relevancia y diversidad
- **`maximize_engagement`**: Prioriza productos con alta probabilidad de interacción
- **`promote_new_arrivals`**: Da mayor peso a productos nuevos
- **`discover_diverse`**: Maximiza la diversidad de recomendaciones

Cada estrategia configura automáticamente varios parámetros internos:

| Estrategia | Descripción | Características |
|------------|-------------|-----------------|
| `balanced` | Equilibrio general | Pesos iguales para colaborativo y contenido, diversidad moderada |
| `maximize_engagement` | Maximizar conversiones | Mayor peso colaborativo, menor diversidad, favorece productos populares |
| `promote_new_arrivals` | Destacar novedades | Mayor peso a contenido, fuerte impulso a productos recientes |
| `discover_diverse` | Descubrimiento | Alta diversidad de categorías, menor énfasis en popularidad |

### Response Body

La respuesta sigue el esquema `PaginatedResponse[Product]`:

```json
{
  "items": [
    {
      "id": 1234,
      "account_id": 567,
      "name": "Smartphone Rayuela X",
      "description": "El último smartphone con IA.",
      "price": 499.99,
      "category": "electronics",
      "average_rating": 4.5,
      "num_ratings": 150,
      "inventory_count": 50,
      "created_at": "2023-10-26T10:00:00+00:00",
      "updated_at": "2023-10-26T11:00:00+00:00",
      "is_active": true,
      "deleted_at": null,
      "score": 0.95,
      "source": "hybrid",
      "explanation": "Recomendado porque usuarios similares a ti compraron este producto y coincide con tus intereses en electrónica."
    },
    // ... otros productos
  ],
  "total": 25,
  "skip": 0,
  "limit": 10
}
```

- **`items`**: Lista de productos recomendados
- **`total`**: Número total de resultados disponibles
- **`skip`**: Número de elementos saltados
- **`limit`**: Número máximo de elementos devueltos

Cada producto incluye:
- Atributos básicos (id, name, price, etc.)
- **`score`**: Puntuación de relevancia (0-1)
- **`source`**: Fuente de la recomendación (collaborative, content, hybrid)
- **`explanation`**: Explicación de la recomendación (si `include_explanation=true`)

### Ejemplos

#### Ejemplo 1: Recomendaciones básicas

```python
import requests

url = "https://api.rayuela.ai/api/v1/recommendations/personalized/query"
headers = {
    "X-API-Key": "tu_api_key",
    "Content-Type": "application/json"
}

payload = {
    "user_id": 123,
    "limit": 5
}

response = requests.post(url, headers=headers, json=payload)
print(response.json())
```

#### Ejemplo 2: Filtros complejos y contexto

```python
payload = {
    "user_id": 123,
    "filters": {
        "logic": "and",
        "filters": [
            {"field": "price", "operator": "lt", "value": 100},
            {"field": "category", "operator": "eq", "value": "electronics"},
            {
                "logic": "or",
                "filters": [
                    {"field": "brand", "operator": "eq", "value": "Samsung"},
                    {"field": "brand", "operator": "eq", "value": "Apple"}
                ]
            }
        ]
    },
    "context": {
        "page_type": "category",
        "device": "mobile",
        "recently_viewed_ids": [456, 789]
    },
    "strategy": "discover_diverse",
    "include_explanation": true,
    "limit": 10
}
```

## Otros Tipos de Recomendaciones

Además del endpoint principal de recomendaciones personalizadas, Rayuela ofrece varios endpoints especializados para casos de uso específicos.

### Productos Similares (`GET /products/{product_id}/similar`)

Obtiene productos similares a un producto específico basándose en características de contenido.

#### Parámetros de Ruta
- **`product_id`** (obligatorio): ID del producto de referencia

#### Parámetros de Consulta
- **`include_explanation`** (opcional, default: false): Incluir explicación de la recomendación
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/products/123/similar?limit=5&include_explanation=true" \
     -H "X-API-Key: tu_api_key"
```

### Productos Más Vendidos (`GET /recommendations/most-sold`)

Obtiene los productos más vendidos en un período de tiempo específico.

#### Parámetros de Consulta
- **`timeframe`** (opcional, default: "week"): Período de tiempo ("day", "week", "month")
- **`category`** (opcional): Filtrar por categoría
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/most-sold?timeframe=month&category=electronics&limit=5" \
     -H "X-API-Key: tu_api_key"
```

### Productos Mejor Valorados (`GET /recommendations/top-rated`)

Obtiene los productos con las mejores valoraciones.

#### Parámetros de Consulta
- **`min_ratings`** (opcional, default: 10): Número mínimo de valoraciones requeridas
- **`category`** (opcional): Filtrar por categoría
- **`include_explanation`** (opcional, default: false): Incluir explicación de la recomendación
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/top-rated?min_ratings=20&category=clothing&limit=5" \
     -H "X-API-Key: tu_api_key"
```

### Tendencias Populares (`GET /recommendations/popular-trends`)

Obtiene productos que están ganando popularidad recientemente.

#### Parámetros de Consulta
- **`include_explanation`** (opcional, default: false): Incluir explicación de la recomendación
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/popular-trends?limit=5" \
     -H "X-API-Key: tu_api_key"
```

### Productos También Comprados (`GET /recommendations/also-bought/{product_id}`)

Obtiene productos que suelen comprarse junto con un producto específico.

#### Parámetros de Ruta
- **`product_id`** (obligatorio): ID del producto de referencia

#### Parámetros de Consulta
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/also-bought/123?limit=5" \
     -H "X-API-Key: tu_api_key"
```

### Productos por Categoría (`GET /recommendations/category/{category}`)

Obtiene productos recomendados dentro de una categoría específica.

#### Parámetros de Ruta
- **`category`** (obligatorio): Nombre de la categoría

#### Parámetros de Consulta
- **`sort_by`** (opcional, default: "popularity"): Campo por el cual ordenar ("popularity", "rating", "price", "newest")
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/category/electronics?sort_by=rating&limit=5" \
     -H "X-API-Key: tu_api_key"
```

### Términos Más Buscados (`GET /recommendations/most-searched`)

Obtiene los términos de búsqueda más populares.

#### Parámetros de Consulta
- **`timeframe`** (opcional, default: "week"): Período de tiempo ("day", "week", "month")
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/most-searched?timeframe=month&limit=10" \
     -H "X-API-Key: tu_api_key"
```

### Búsquedas Tendencia (`GET /recommendations/trending-searches`)

Obtiene términos de búsqueda que están ganando popularidad recientemente.

#### Parámetros de Consulta
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/trending-searches?limit=10" \
     -H "X-API-Key: tu_api_key"
```

### Búsquedas Relacionadas (`GET /recommendations/related-searches/{product_id}`)

Obtiene términos de búsqueda relacionados con un producto específico.

#### Parámetros de Ruta
- **`product_id`** (obligatorio): ID del producto de referencia

#### Parámetros de Consulta
- **`skip`** (opcional, default: 0): Número de elementos a saltar
- **`limit`** (opcional, default: 10): Número máximo de elementos a devolver

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/related-searches/123?limit=5" \
     -H "X-API-Key: tu_api_key"
```

## Explicaciones de Recomendación

Rayuela proporciona explicaciones detalladas sobre por qué se recomienda un producto específico a un usuario, lo que aumenta la transparencia y la confianza en el sistema.

### Explicación Detallada (`GET /recommendations/explain/{end_user_id}/{item_id}`)

Obtiene una explicación detallada de por qué se recomienda un producto específico a un usuario.

#### Parámetros de Ruta
- **`end_user_id`** (obligatorio): ID del usuario
- **`item_id`** (obligatorio): ID del producto

#### Response Body

La respuesta sigue el esquema `DetailedExplanation`:

```json
{
  "primary_reason": "similar_items",
  "secondary_reasons": ["category_affinity", "trending_item"],
  "confidence": 0.85,
  "text_explanation": "Recomendado porque es similar a productos que te han gustado",
  "evidence": [
    {
      "type": "product",
      "id": 123,
      "name": "Smartphone Galaxy S21",
      "relevance": 0.85
    }
  ],
  "source": "hybrid"
}
```

- **`primary_reason`**: Razón principal de la recomendación
- **`secondary_reasons`**: Razones secundarias
- **`confidence`**: Confianza en la recomendación (0-1)
- **`text_explanation`**: Explicación en texto plano
- **`evidence`**: Evidencia que respalda la explicación
- **`source`**: Fuente de la recomendación (collaborative, content, hybrid)

#### Razones de Recomendación

- **`similar_users`**: Usuarios similares compraron/vieron este producto
- **`similar_items`**: Similar a productos que le gustaron al usuario
- **`category_affinity`**: Usuario tiene afinidad con esta categoría
- **`popular_item`**: Producto popular en general
- **`trending_item`**: Producto con tendencia al alza
- **`attribute_match`**: Coincide con atributos preferidos por el usuario
- **`complementary_item`**: Complementa productos que el usuario ya tiene
- **`recent_interaction`**: Basado en interacciones recientes

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/explain/123/456" \
     -H "X-API-Key: tu_api_key"
```

### Métricas de Confianza (`GET /recommendations/confidence-metrics`)

Obtiene métricas de confianza para las recomendaciones de la cuenta actual.

#### Response Body

```json
{
  "overall_confidence": 0.85,
  "metrics": {
    "coverage": 0.92,
    "diversity": 0.78,
    "novelty": 0.65,
    "relevance": 0.88
  },
  "model_info": {
    "model_type": "hybrid",
    "last_trained": "2023-10-26T10:00:00Z",
    "data_points": 15000
  }
}
```

#### Ejemplo

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/confidence-metrics" \
     -H "X-API-Key: tu_api_key"
```

## Invalidación de Caché

Rayuela utiliza caché para mejorar el rendimiento de las recomendaciones. Sin embargo, en algunos casos es necesario invalidar la caché para obtener recomendaciones actualizadas.

### Invalidar Caché por Usuario (`POST /recommendations/invalidate-cache/{end_user_id}`)

Invalida la caché de recomendaciones para un usuario específico.

#### Parámetros de Ruta
- **`end_user_id`** (obligatorio): ID del usuario

#### Cuándo Usar

- Después de una compra importante
- Después de un cambio significativo en el perfil del usuario
- Cuando el usuario reporta recomendaciones irrelevantes

#### Ejemplo

```bash
curl -X POST "https://api.rayuela.ai/api/v1/recommendations/invalidate-cache/123" \
     -H "X-API-Key: tu_api_key"
```

### Invalidar Caché por Cuenta (`POST /recommendations/invalidate-cache`)

Invalida la caché de recomendaciones para toda la cuenta.

#### Cuándo Usar

- Después de re-entrenar un modelo
- Después de una actualización importante del catálogo
- Después de cambios en la configuración de recomendaciones

#### Ejemplo

```bash
curl -X POST "https://api.rayuela.ai/api/v1/recommendations/invalidate-cache" \
     -H "X-API-Key: tu_api_key"
```

## Mejores Prácticas

1. **Utilice el endpoint principal**: Para la mayoría de los casos de uso, `POST /recommendations/personalized/query` es la mejor opción debido a su flexibilidad.

2. **Proporcione contexto**: Incluya información contextual para mejorar la relevancia de las recomendaciones.

3. **Elija la estrategia adecuada**: Seleccione la estrategia que mejor se adapte a sus objetivos de negocio.

4. **Utilice filtros**: Refine las recomendaciones según criterios específicos.

5. **Monitoree las métricas de confianza**: Verifique regularmente la calidad de las recomendaciones.

6. **Invalide la caché cuando sea necesario**: Asegúrese de que los usuarios reciban recomendaciones actualizadas después de eventos importantes.

7. **Incluya explicaciones**: Las explicaciones aumentan la transparencia y la confianza en el sistema de recomendación.