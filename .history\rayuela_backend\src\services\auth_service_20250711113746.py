"""
Servicio centralizado para manejar la autenticación.
"""

from datetime import datetime, timezone
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update
from jose import jwt, JWTError
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from src.core.config import settings
from src.core.security import (
    get_password_hash,
    verify_password,
    create_access_token,
    revoke_token,
    is_token_revoked,
)
from src.core.exceptions import (
    InvalidCredentialsError,
    RateLimitExceededError,
    EmailNotVerifiedError,
)
from src.db.models import Account, SystemUser, Subscription
from src.db.enums import SubscriptionPlan
from src.db.repositories import AccountRepository, SystemUserRepository
from src.utils.base_logger import log_info, log_error, log_warning
from src.services.email_verification_service import EmailVerificationService
from src.services.api_key_service import Api<PERSON>eyService
from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id


class AuthService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.account_repo = AccountRepository(db)
        self.api_key_service = ApiKeyService(db)

    async def _check_global_email_uniqueness(self, email: str) -> bool:
        """
        Verifica si el email es globalmente único en todas las cuentas.
        
        SEGURIDAD: Esta función INTENCIONALMENTE realiza una consulta global
        sin filtro de tenant para verificar unicidad de email a nivel sistema.
        Esto es requerido por el diseño de negocio que establece que los emails
        deben ser únicos globalmente para prevenir confusión entre usuarios.
        
        Note: Con la nueva restricción de unicidad a nivel de base de datos,
        esta función sirve principalmente para proporcionar mensajes de error
        más amigables antes de intentar la inserción.

        Args:
            email: Email a verificar

        Returns:
            True si el email es único globalmente, False si ya existe
        """
        try:
            # SEGURIDAD: Log de operación global sin contexto de tenant
            current_tenant = get_current_tenant_id()
            log_info(f"Verificación global de email uniqueness - tenant_context: {current_tenant}, email: {email[:3]}***")
            
            # SEGURIDAD: Consulta GLOBAL INTENCIONADA - sin filtro de tenant
            # Esta es una operación de negocio requerida para unicidad global de emails
            query = select(SystemUser).filter(SystemUser.email == email)
            result = await self.db.execute(query)
            existing_user = result.scalars().first()

            is_unique = existing_user is None
            log_info(f"Email uniqueness check result: {'unique' if is_unique else 'exists'}")
            return is_unique
        except SQLAlchemyError as e:
            log_error(f"Error checking global email uniqueness: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error validating email uniqueness",
            )

    async def authenticate_with_jwt(
        self, token: str
    ) -> Tuple[Optional[Account], Optional[SystemUser]]:
        """
        Autentica usando un token JWT.
        Retorna una tupla (account, user) o (None, None) si falla.
        """
        try:
            payload = jwt.decode(
                token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
            )
            account_id = payload.get("account_id")
            user_id = payload.get("sub")

            if not account_id or not user_id:
                return None, None

            # Verificar si el token ha sido revocado
            if await is_token_revoked(token):
                return None, None

            # Obtener la cuenta
            account = await self.account_repo.get_by_id(account_id)
            if not account or not getattr(account, "is_active", False):
                return None, None

            # Crear repo de usuario con account_id y obtener el usuario
            user_repo = SystemUserRepository(self.db, account_id)
            user = await user_repo.get_by_id(user_id)
            if not user or not getattr(user, "is_active", False):
                return None, None

            return account, user

        except JWTError:
            return None, None

    async def authenticate_with_api_key(self, api_key: str) -> Optional[Account]:
        """
        Autentica usando una API Key.
        Retorna la cuenta o None si falla.
        """
        try:
            # Validar API Key usando el nuevo servicio
            api_key_model = await self.api_key_service.validate_api_key(api_key)
            if not api_key_model:
                return None

            # Obtener la cuenta asociada
            account = await self.account_repo.get_by_id(api_key_model.account_id)
            if not account or not getattr(account, "is_active", False):
                return None

            return account
        except Exception:
            return None

    async def get_current_account(
        self, token: Optional[str] = None, api_key: Optional[str] = None
    ) -> Account:
        """
        Obtiene la cuenta actual, priorizando JWT sobre API Key.
        """
        # 1. Intentar con JWT
        if token:
            account, _ = await self.authenticate_with_jwt(token)
            if account:
                log_info(f"Authenticated via JWT for account {account.account_id}")
                return account

        # 2. Intentar con API Key
        if api_key:
            account = await self.authenticate_with_api_key(api_key)
            if account:
                log_info(f"Authenticated via API Key for account {account.account_id}")
                return account

        # 3. Si ambos fallan, lanzar error
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    async def register_account(
        self, account_name: str, email: str, password: str
    ) -> Dict[str, Any]:
        """
        Registra una nueva cuenta con verificación de unicidad global de email.
        
        SEGURIDAD: Este método opera sin contexto de tenant inicial ya que
        está creando una nueva cuenta. Establece el contexto después de la creación.

        Args:
            account_name: Nombre de la cuenta/organización
            email: Email del administrador (debe ser globalmente único)
            password: Contraseña del administrador

        Returns:
            Diccionario con token de acceso y API key

        Raises:
            HTTPException: Si el email ya existe globalmente o hay otros errores
        """
        try:
            # SEGURIDAD: Log de operación de registro sin contexto de tenant inicial
            current_tenant = get_current_tenant_id()
            log_info(f"Account registration attempt - tenant_context: {current_tenant}, email: {email[:3]}***")
            
            # VERIFICACIÓN TRANSACCIONAL DE UNICIDAD GLOBAL
            async with self.db.begin():
                # 1. Verificar unicidad global del email de manera atómica
                is_email_unique = await self._check_global_email_uniqueness(email)
                if not is_email_unique:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Email already exists globally. Please use a different email address.",
                    )

                # 2. Crear la cuenta primero
                from src.db.schemas import AccountCreate

                account_data = AccountCreate(
                    name=account_name,
                    is_active=True,
                )

                account = await self.account_repo.create(account_data)
                await self.db.flush()  # Obtener el account_id generado

                # 3. Crear el usuario administrador con verificación adicional de unicidad
                user_repo = SystemUserRepository(self.db, account.account_id)

                # Verificación adicional dentro de la transacción por seguridad
                final_check = await self._check_global_email_uniqueness(email)
                if not final_check:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Email was registered by another process. Please try with a different email.",
                    )

                from src.db.schemas import SystemUserCreate

                user_data = SystemUserCreate(email=email, password=password)

                system_user = await user_repo.create(user_data)
                await self.db.flush()

                # 4. Generar la primera API Key
                api_key, api_key_model = (
                    await self.api_key_service.create_api_key(account.account_id, "Default API Key")
                )

                # SEGURIDAD: Establecer contexto de tenant para la nueva cuenta
                log_info(f"Registration successful - establishing tenant context: {account.account_id}")
                set_current_tenant_id(account.account_id)

                # 5. Generar token JWT
                access_token = create_access_token(
                    data={
                        "sub": str(system_user.id),
                        "account_id": str(account.account_id),
                    }
                )

                log_info(
                    f"Successfully registered new account {account.account_id} with email {email[:3]}***"
                )

                return {
                    "access_token": access_token,
                    "token_type": "bearer",
                    "account_id": account.account_id,
                    "user_id": system_user.id,
                    "is_admin": system_user.is_admin,
                    "api_key": api_key,
                    "message": "Account registered successfully. This is your API Key - save it securely as it will only be shown once.",
                }

        except HTTPException:
            # Re-raise HTTPExceptions (como email duplicado)
            raise
        except IntegrityError as e:
            # Manejar violaciones de restricción de unicidad de email a nivel de base de datos
            if "idx_system_user_email_global" in str(e.orig) or "email" in str(e.orig).lower():
                log_warning(f"Database constraint violation for email {email}: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already exists globally. Please use a different email address.",
                )
            else:
                log_error(f"Database integrity error during registration: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Error during registration",
                )
        except Exception as e:
            log_error(f"Registration error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error during registration",
            )

    async def login(
        self, email: str, password: str, redis_client: Any = None
    ) -> Dict[str, Any]:
        """
        Autentica un usuario y genera un token JWT.
        
        SEGURIDAD: Este método realiza autenticación global por email ya que
        los usuarios pueden no saber su account_id durante el login.
        """
        try:
            # SEGURIDAD: Log de operación de login sin contexto de tenant
            current_tenant = get_current_tenant_id()
            log_info(f"Login attempt - tenant_context: {current_tenant}, email: {email[:3]}***")
            
            # Verificar intentos de login
            if redis_client:
                await self._check_login_attempts(redis_client, email)

            # SEGURIDAD: Consulta GLOBAL INTENCIONADA para autenticación
            # Los usuarios no conocen su account_id durante login, por lo que
            # la búsqueda inicial debe ser global por email
            query = select(SystemUser).filter(SystemUser.email == email)
            result = await self.db.execute(query)
            user = result.scalars().first()

            if not user or not getattr(user, "is_active", False):
                if redis_client:
                    await self._increment_login_attempts(redis_client, email)
                log_warning(f"Login failed - user not found or inactive: {email[:3]}***")
                raise InvalidCredentialsError("Incorrect email or password")

            # Verificar contraseña
            if not verify_password(password, str(user.hashed_password)):
                if redis_client:
                    await self._increment_login_attempts(redis_client, email)
                log_warning(f"Login failed - incorrect password: {email[:3]}***")
                raise InvalidCredentialsError("Incorrect email or password")
            
            # SEGURIDAD: Establecer contexto de tenant después de autenticación exitosa
            log_info(f"Login successful - establishing tenant context: {user.account_id}")
            set_current_tenant_id(user.account_id)

            # Verificar email para cuentas FREE
            subscription = await self._get_active_subscription(user.account_id)
            if subscription and subscription.plan_type == SubscriptionPlan.FREE:
                if not getattr(user, "email_verified", False):
                    # Enviar email de verificación
                    email_service = EmailVerificationService(self.db, user.account_id)
                    await email_service.send_verification_email(
                        user_id=user.id, email=user.email
                    )
                    raise EmailNotVerifiedError(
                        "Email not verified. Verification email sent."
                    )

            # Actualizar last_login_at en una transacción separada
            current_time = datetime.now(timezone.utc)
            async with self.db.begin():
                # SEGURIDAD: Filtros manuales son CRÍTICOS para aislamiento de tenants
                # SEGURIDAD: Actualización con filtro explícito por account_id para prevenir
                # modificaciones accidentales de usuarios en otros tenants
                await self.db.execute(
                    update(SystemUser)
                    .where(
                        SystemUser.account_id == user.account_id,  # CRÍTICO: Filtro por tenant
                        SystemUser.id == user.id,
                    )
                    .values(last_login_at=current_time, updated_at=current_time)
                )
                log_info(
                    f"Updated last_login_at for user {user.id} in account {user.account_id}"
                )

            # Generar token
            access_token = create_access_token(
                data={"sub": str(user.id), "account_id": str(user.account_id)}
            )

            # Resetear intentos de login
            if redis_client:
                await self._reset_login_attempts(redis_client, email)

            return {
                "access_token": access_token,
                "token_type": "bearer",
                "account_id": user.account_id,
                "is_admin": user.is_admin,
            }

        except HTTPException:
            # Re-lanzar excepciones HTTP específicas (credenciales inválidas,
            # rate-limit, email no verificado, etc.) para que el endpoint las
            # devuelva con su status_code correcto.
            raise
        except Exception as e:
            log_error(f"Login error: {str(e)}")
            # SEGURIDAD: Limpiar contexto en caso de error inesperado
            set_current_tenant_id(None)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error during login",
            )

    async def logout(self, token: str) -> None:
        """
        Revoca un token JWT.
        """
        await revoke_token(token)

    async def _get_active_subscription(self, account_id: int) -> Optional[Subscription]:
        """Obtiene la suscripción activa de una cuenta."""
        query = select(Subscription).where(
            Subscription.account_id == account_id, Subscription.is_active == True
        )
        result = await self.db.execute(query)
        return result.scalars().first()

    async def _check_login_attempts(self, redis: Any, email: str) -> None:
        """Verifica intentos de login."""
        key = f"login_attempts:{email}"
        attempts = await redis.get(key)

        if attempts and int(attempts) >= settings.MAX_LOGIN_ATTEMPTS:
            cooldown_key = f"login_cooldown:{email}"
            if await redis.exists(cooldown_key):
                raise RateLimitExceededError(
                    f"Too many failed attempts. Please try again in {settings.LOGIN_COOLDOWN_MINUTES} minutes"
                )
            await redis.delete(key)

    async def _increment_login_attempts(self, redis: Any, email: str) -> None:
        """Incrementa el contador de intentos de login."""
        key = f"login_attempts:{email}"
        await redis.incr(key)
        await redis.expire(key, settings.LOGIN_ATTEMPT_WINDOW_MINUTES * 60)

    async def _reset_login_attempts(self, redis: Any, email: str) -> None:
        """Resetea el contador de intentos de login."""
        key = f"login_attempts:{email}"
        await redis.delete(key)
