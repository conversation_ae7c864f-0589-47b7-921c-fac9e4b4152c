# Documentación de la API de Rayuela

Bienvenido a la documentación oficial de la API de Rayuela, una plataforma de recomendaciones personalizada para empresas.

## Contenido

1. [Introducción a Rayuela](../introduction.md)
2. [Guía de Inicio Rápido](../quickstart.md)
3. [Autenticación](authentication.md)
4. [Conceptos Clave](../concepts.md)
5. [Ingesta de Datos](../guides/data_ingestion_guide.md)
6. [Recomendaciones](recommendations.md)
7. [Pipeline de Entrenamiento](pipeline.md)
8. [Uso, Límites y Facturación](usage.md)
9. [Manejo <PERSON>rro<PERSON>](errors.md)
10. [Roles y Permisos](roles.md)
11. [Referencia de API](reference.md)
12. [Ejemplos de Código y SDKs](../examples/index.md)
13. [Webhooks](webhooks.md)
14. [Soporte y Ayuda](../support.md)

## Visión General de la API

La API de Rayuela sigue los principios RESTful y utiliza JSON para el intercambio de datos. Todos los endpoints están disponibles en la base URL:

```
https://api.rayuela.ai/api/v1
```

### Características Principales

- **Autenticación basada en API Key**: Segura y fácil de implementar
- **Respuestas JSON**: Formato estándar para todas las respuestas
- **Paginación**: Para endpoints que devuelven múltiples resultados
- **Filtrado**: Opciones avanzadas de filtrado para endpoints de consulta
- **Manejo de errores**: Códigos de estado HTTP estándar y mensajes de error descriptivos
- **Documentación interactiva**: Disponible en `/api/docs` (Swagger/OpenAPI)

### Endpoints Principales

| Categoría | Endpoint Base | Descripción |
|-----------|---------------|-------------|
| Autenticación | `/auth` | Registro, login y gestión de API Keys |
| Ingesta de Datos | `/ingestion` | Carga de datos de usuarios, productos e interacciones |
| Recomendaciones | `/recommendations` | Obtención de recomendaciones personalizadas |
| Pipeline | `/pipeline` | Entrenamiento y gestión de modelos |
| Uso | `/usage` | Consulta de uso y límites |
| Facturación | `/billing` | Gestión de suscripciones y pagos |

## Primeros Pasos

Para comenzar a utilizar la API de Rayuela, siga estos pasos:

1. [Regístrese](../quickstart.md#paso-1-registrar-una-cuenta) para obtener una cuenta
2. [Obtenga su API Key](../quickstart.md#paso-2-obtener-tu-primera-api-key)
3. [Cargue sus datos](../guides/data_ingestion_guide.md)
4. [Entrene su primer modelo](pipeline.md#iniciar-entrenamiento-post-pipelinetrain)
5. [Obtenga recomendaciones](recommendations.md#recomendaciones-personalizadas-post-recommendationspersonalizedquery)

## Ejemplos de Código

### Python

```python
import requests

API_KEY = "tu_api_key"
BASE_URL = "https://api.rayuela.ai/api/v1"

# Autenticación
headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

# Obtener recomendaciones personalizadas
response = requests.post(
    f"{BASE_URL}/recommendations/personalized/query",
    headers=headers,
    json={
        "user_id": 123,
        "limit": 5
    }
)

recommendations = response.json()
print(recommendations)
```

### Node.js

```javascript
const axios = require('axios');

const API_KEY = 'tu_api_key';
const BASE_URL = 'https://api.rayuela.ai/api/v1';

// Autenticación
const headers = {
    'X-API-Key': API_KEY,
    'Content-Type': 'application/json'
};

// Obtener recomendaciones personalizadas
axios.post(
    `${BASE_URL}/recommendations/personalized/query`,
    {
        user_id: 123,
        limit: 5
    },
    { headers }
)
.then(response => {
    const recommendations = response.data;
    console.log(recommendations);
})
.catch(error => {
    console.error('Error:', error.response ? error.response.data : error.message);
});
```

### cURL

```bash
curl -X POST "https://api.rayuela.ai/api/v1/recommendations/personalized/query" \
     -H "X-API-Key: tu_api_key" \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": 123,
       "limit": 5
     }'
```

## Soporte

Si necesita ayuda con la API de Rayuela, puede:

- Consultar la [documentación detallada](../index.md)
- Revisar los [ejemplos de código](../examples/index.md)
- Contactar al [soporte técnico](../support.md)
- Unirse a nuestra [comunidad de desarrolladores](https://community.rayuela.ai)

## Cambios en la API

Rayuela sigue las prácticas de versionado semántico para la API. Los cambios importantes se anunciarán con anticipación y se mantendrán versiones anteriores durante períodos de transición.

Para mantenerse informado sobre cambios y nuevas características, suscríbase a nuestro [boletín de desarrolladores](https://rayuela.ai/developers/newsletter).

# Referencia de la API de Rayuela

Esta documentación proporciona una referencia técnica para todos los endpoints disponibles en la API de Rayuela.

## Información General

**Base URL:** `https://api.rayuela.ai/api/v1`

## Autenticación

La API de Rayuela utiliza dos métodos de autenticación diferentes según el tipo de operación:

### API Keys

Para la mayoría de las llamadas a la API (ingesta de datos, obtención de recomendaciones, etc.), debes incluir tu API Key en el encabezado `X-API-Key`:

```
X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

Ejemplo con cURL:

```bash
curl -X GET "https://api.rayuela.ai/api/v1/health/auth" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

### JWT (JSON Web Tokens)

Para operaciones relacionadas con la gestión de la cuenta, usuarios del sistema, roles y facturación, debes usar un token JWT en el encabezado `Authorization`:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Ejemplo con cURL:

```bash
curl -X GET "https://api.rayuela.ai/api/v1/accounts/current" \
     -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## Diferencias entre API Keys y JWT

- **API Key**: Se usa para la operación diaria de la API (obtener recomendaciones, ingestar datos, etc.). Identifica una *cuenta*.
- **JWT**: Se usa para gestionar la configuración de la cuenta. Identifica un *usuario del sistema* dentro de una cuenta.

## Identificadores de Usuario: Internos vs Externos

La API de Rayuela maneja dos tipos de identificadores de usuario:

### `user_id` (ID Interno)
- **Tipo**: Número entero
- **Generado por**: Sistema de Rayuela automáticamente
- **Uso**: Identificación interna en la base de datos
- **Ejemplo**: `123`, `456`, `789`
- **Cuándo usar**: Cuando tienes el ID interno de una respuesta previa de la API

### `external_user_id` (ID Externo)
- **Tipo**: Cadena de texto
- **Generado por**: Tu sistema/aplicación
- **Uso**: Identificación usando tus propios identificadores
- **Ejemplo**: `"user_abc123"`, `"customer_xyz789"`, `"<EMAIL>"`
- **Cuándo usar**: Recomendado para la mayoría de casos de uso

### Recomendación
**Usa `external_user_id` siempre que sea posible** ya que te permite usar tus propios identificadores sin necesidad de mapear IDs internos.

## Endpoints

### Auth

#### Registro

```
POST /auth/register
```

Registra una nueva cuenta y un usuario administrador inicial.

**Ejemplo con cURL:**

```bash
curl -X POST "https://api.rayuela.ai/api/v1/auth/register" \
     -H "Content-Type: application/json" \
     -d '{
       "account_name": "Mi Empresa",
       "email": "<EMAIL>",
       "password": "MiContraseñaSegura123"
     }'
```

**Respuesta:**

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "account_id": 123,
  "is_admin": true,
  "api_key": "sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "message": "Registro exitoso. Esta es tu API Key, guárdala en un lugar seguro."
}
```

**Importante:** La API Key solo se muestra una vez durante el registro. Asegúrate de guardarla de forma segura.

#### Obtener Token JWT

```
POST /auth/token
```

Obtiene un token JWT para autenticación de usuario del sistema.

**Ejemplo con cURL:**

```bash
curl -X POST "https://api.rayuela.ai/api/v1/auth/token" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=<EMAIL>&password=MiContraseñaSegura123"
```

**Respuesta:**

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### Ingesta de Datos

#### Ingesta por Lotes

```
POST /ingestion/batch
```

Ingesta productos, usuarios y/o interacciones en un solo lote.

**Ejemplo con cURL:**

```bash
curl -X POST "https://api.rayuela.ai/api/v1/ingestion/batch" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" \
     -H "Content-Type: application/json" \
     -d '{
       "products": [
         {
           "externalId": "P001",
           "name": "Smartphone XYZ",
           "category": "Electronics",
           "price": 599.99,
           "description": "Un smartphone de última generación",
           "averageRating": 4.5,
           "numRatings": 120,
           "inventoryCount": 50
         }
       ],
       "users": [
         {
           "externalId": "U001",
           "preferredCategories": ["electronics", "books"],
           "dislikedCategories": ["sports"],
           "priceRangeMin": 10.50,
           "priceRangeMax": 500.00,
           "demographicInfo": {
             "age": 28,
             "gender": "M",
             "location": "urban"
           }
         }
       ],
       "interactions": [
         {
           "userId": 1,
           "productId": 1,
           "interactionType": "VIEW",
           "value": 1.0,
           "recommendationMetadata": {
             "source": "homepage",
             "algorithm": "collaborative_filtering"
           }
         }
       ]
     }'
```

**Respuesta:**

```json
{
  "job_id": "job_123456",
  "status": "processing",
  "message": "Ingesta iniciada correctamente"
}
```

#### Consultar Estado de Ingesta por Lotes

```
GET /ingestion/batch/{job_id}
```

Consulta el estado de un trabajo de ingesta por lotes.

**Ejemplo con cURL:**

```bash
curl -X GET "https://api.rayuela.ai/api/v1/ingestion/batch/job_123456" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

**Respuesta:**

```json
{
  "job_id": "job_123456",
  "status": "completed",
  "progress": 100,
  "products_processed": 1,
  "users_processed": 1,
  "interactions_processed": 1,
  "errors": [],
  "started_at": "2023-10-25T15:30:00Z",
  "completed_at": "2023-10-25T15:31:00Z"
}
```

### Pipeline de Entrenamiento

#### Iniciar Entrenamiento

```
POST /pipeline/train
```

Inicia un trabajo de entrenamiento para generar un nuevo modelo de recomendación.

**Ejemplo con cURL:**

```bash
curl -X POST "https://api.rayuela.ai/api/v1/pipeline/train" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" \
     -H "Content-Type: application/json" \
     -d '{
       "model_type": "standard",
       "force": true
     }'
```

**Respuesta:**

```json
{
  "job_id": "train_789012",
  "status": "queued",
  "message": "Trabajo de entrenamiento en cola"
}
```

#### Consultar Estado de Entrenamiento

```
GET /pipeline/jobs/{job_id}/status
```

Consulta el estado de un trabajo de entrenamiento.

**Ejemplo con cURL:**

```bash
curl -X GET "https://api.rayuela.ai/api/v1/pipeline/jobs/train_789012/status" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

**Respuesta:**

```json
{
  "job_id": "train_789012",
  "status": "running",
  "progress": 75,
  "message": "Entrenando modelo",
  "started_at": "2023-10-25T16:00:00Z",
  "estimated_completion": "2023-10-25T16:10:00Z"
}
```

#### Listar Modelos Entrenados

```
GET /pipeline/models
```

Lista todos los modelos entrenados para la cuenta.

**Ejemplo con cURL:**

```bash
curl -X GET "https://api.rayuela.ai/api/v1/pipeline/models?skip=0&limit=10" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

**Respuesta:**

```json
{
  "items": [
    {
      "id": 456,
      "model_type": "standard",
      "artifact_version": "v1.2.3",
      "created_at": "2023-10-25T16:05:00Z",
      "is_active": true,
      "metrics": {
        "precision": 0.85,
        "recall": 0.78,
        "f1_score": 0.81
      }
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 10
}
```

### Recomendaciones

#### Recomendaciones Personalizadas (Endpoint Principal)

```
POST /recommendations/personalized/query
```

Obtiene recomendaciones personalizadas para un usuario específico.

**Ejemplo con cURL:**

```bash
curl -X POST "https://api.rayuela.ai/api/v1/recommendations/personalized/query" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "U001",
       "filters": {
         "logic": "and",
         "filters": [
           {
             "field": "category",
             "op": "eq",
             "value": "Electronics"
           },
           {
             "field": "price",
             "op": "lt",
             "value": 1000
           }
         ]
       },
       "context": {
         "page_type": "home",
         "device": "mobile"
       },
       "strategy": "balanced",
       "include_explanation": true,
       "limit": 5
     }'
```

**Respuesta:**

```json
{
  "items": [
    {
      "id": "P001",
      "name": "Smartphone XYZ",
      "description": "Un smartphone de última generación",
      "price": 599.99,
      "category": "Electronics",
      "image_url": "https://ejemplo.com/images/p001.jpg",
      "score": 0.95,
      "source": "hybrid",
      "explanation": "Recomendado porque has visto productos similares y coincide con tus intereses"
    }
  ],
  "total": 20,
  "skip": 0,
  "limit": 5
}
```

#### Productos Similares

```
GET /products/{product_id}/similar
```

Obtiene productos similares a un producto específico.

**Ejemplo con cURL:**

```bash
curl -X GET "https://api.rayuela.ai/api/v1/products/P001/similar?limit=4" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

**Respuesta:**

```json
{
  "items": [
    {
      "id": "P002",
      "name": "Smartphone ABC",
      "description": "Otro smartphone de gama alta",
      "price": 649.99,
      "category": "Electronics",
      "image_url": "https://ejemplo.com/images/p002.jpg",
      "similarity_score": 0.92
    }
  ],
  "total": 10,
  "skip": 0,
  "limit": 4
}
```

#### Productos Más Vendidos

```
GET /recommendations/most-sold
```

Obtiene los productos más vendidos.

**Ejemplo con cURL:**

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/most-sold?limit=5" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

**Respuesta:**

```json
{
  "items": [
    {
      "id": "P003",
      "name": "Auriculares Inalámbricos",
      "description": "Auriculares con cancelación de ruido",
      "price": 129.99,
      "category": "Electronics",
      "image_url": "https://ejemplo.com/images/p003.jpg",
      "sales_count": 1500
    }
  ],
  "total": 100,
  "skip": 0,
  "limit": 5
}
```

#### Explicaciones Detalladas de Recomendación

```
GET /recommendations/explain/{user_id}/{item_id}
```

Obtiene una explicación detallada de por qué se recomendó un producto específico a un usuario.

**Parámetros:**
- `user_id`: ID interno del usuario (número entero)
- `item_id`: ID interno del producto (número entero)

**Ejemplo con cURL:**

```bash
curl -X GET "https://api.rayuela.ai/api/v1/recommendations/explain/123/456" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

**Respuesta:**

```json
{
  "user_id": "U001",
  "item_id": "P001",
  "score": 0.95,
  "primary_reason": {
    "type": "SIMILAR_ITEMS_VIEWED",
    "confidence": 0.85,
    "items": ["P004", "P007"]
  },
  "secondary_reasons": [
    {
      "type": "CATEGORY_AFFINITY",
      "confidence": 0.75,
      "category": "Electronics"
    }
  ],
  "text_explanation": "Este producto se recomendó principalmente porque has visto productos similares como 'Tablet XYZ' y 'Smartwatch ABC'. También coincide con tu interés en la categoría 'Electronics'."
}
```

#### Invalidación de Caché de Recomendaciones

```
POST /recommendations/invalidate-cache/{end_user_id}
```

Invalida el caché de recomendaciones para un usuario específico.

**Ejemplo con cURL:**

```bash
curl -X POST "https://api.rayuela.ai/api/v1/recommendations/invalidate-cache/U001" \
     -H "X-API-Key: sk_prod_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

**Respuesta:**

```json
{
  "success": true,
  "message": "Caché invalidado correctamente para el usuario U001"
}
```

### Uso y Límites

#### Resumen de Uso

```
GET /usage/summary
```

Obtiene un resumen del uso actual de la API y los límites del plan.

**Ejemplo con cURL:**

```bash
curl -X GET "https://api.rayuela.ai/api/v1/usage/summary" \
     -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Respuesta:**

```json
{
  "account_id": 123,
  "plan": "PROFESSIONAL",
  "current_period_start": "2023-10-01T00:00:00Z",
  "current_period_end": "2023-10-31T23:59:59Z",
  "api_calls": {
    "used": 7500,
    "limit": 100000,
    "percentage": 7.5
  },
  "storage": {
    "used_mb": 1500,
    "limit_mb": 10000,
    "percentage": 15
  },
  "items": {
    "count": 5000,
    "limit": 100000,
    "percentage": 5
  },
  "users": {
    "count": 15000,
    "limit": 500000,
    "percentage": 3
  },
  "training": {
    "last_trained": "2023-10-20T14:30:00Z",
    "next_available": "2023-10-27T14:30:00Z",
    "frequency_days": 7
  }
}
```

## Códigos de Estado HTTP

La API utiliza los siguientes códigos de estado HTTP:

- `200 OK`: La solicitud se completó correctamente
- `201 Created`: El recurso se creó correctamente
- `400 Bad Request`: La solicitud es inválida
- `401 Unauthorized`: Autenticación requerida
- `403 Forbidden`: No tiene permisos para acceder al recurso
- `404 Not Found`: El recurso no existe
- `422 Unprocessable Entity`: Error de validación
- `429 Too Many Requests`: Límite de tasa excedido
- `500 Internal Server Error`: Error del servidor

## Códigos de Error

La API utiliza códigos de error estandarizados para facilitar el manejo programático de errores. Consulta la [documentación de códigos de error](../../docs/ERROR_CODES.md) para más información.

## Versionado de la API

La API está versionada en la URL (`/api/v1/`). Nos comprometemos a mantener la compatibilidad con versiones anteriores y a notificar con anticipación cuando se depreciará una versión.

## Contacto y Soporte

Si tienes preguntas o necesitas ayuda, ponte en contacto con nuestro equipo de soporte:

- Email: <EMAIL>
- Sitio web: https://rayuela.ai/support
