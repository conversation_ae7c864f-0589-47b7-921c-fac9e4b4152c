(()=>{var e={};e.id=9747,e.ids=[9747],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51011:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d={children:["",{children:["(dashboard)",{children:["pipeline",{children:["ingestion-jobs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53697)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,57675)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/pipeline/ingestion-jobs/page",pathname:"/pipeline/ingestion-jobs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},53697:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode_workspace\\\\cloned_repos\\\\rayuela\\\\rayuela_frontend\\\\src\\\\app\\\\(dashboard)\\\\pipeline\\\\ingestion-jobs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(dashboard)\\pipeline\\ingestion-jobs\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61611:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64680:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>L});var r=t(60687),a=t(43210),n=t(44493),i=t(29523),o=t(85726),l=t(6211),d=t(91821),c=t(16023),x=t(61611),u=t(93613),h=t(80462),m=t(99270),p=t(13861),j=t(31158),g=t(13943),v=t(85650),f=t(41585),N=t(89667),b=t(80013),y=t(15079),w=t(63503),_=t(89571),A=t(62796),C=t(5336);let S=(0,t(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var I=t(41862);function P({onIngestionStart:e,trigger:s}){let[t,n]=(0,a.useState)(!1),[o,l]=(0,a.useState)("batch"),[x,h]=(0,a.useState)(null),[m,p]=(0,a.useState)(!1),[j,g]=(0,a.useState)(null),[v,f]=(0,a.useState)(!1),_=async()=>{if(!x)return void g("Por favor selecciona un archivo");p(!0),g(null);try{let s,t=await A(x);try{s=x.name.toLowerCase().endsWith(".json")?JSON.parse(t):x.name.toLowerCase().endsWith(".csv")?await k(t,o):JSON.parse(t)}catch{throw Error("Error al parsear el archivo. Aseg\xfarate de que el formato sea v\xe1lido.")}let r={...s};console.log("Sending batch data:",JSON.stringify(r,null,2));let a=await e(r);console.log("Ingestion started:",a),f(!0),setTimeout(()=>{n(!1),f(!1),h(null),l("batch")},2e3)}catch(e){g(e instanceof Error?e.message:"Error iniciando ingesta de datos")}finally{p(!1)}},A=e=>new Promise((s,t)=>{let r=new FileReader;r.onload=e=>s(e.target?.result),r.onerror=()=>t(Error("Error reading file")),r.readAsText(e)}),P=e=>e.replace(/_([a-z])/g,(e,s)=>s.toUpperCase()),E=e=>{let s={};for(let[t,r]of Object.entries(e))s[P(t)]=r;return s},k=async(e,s)=>{let t=e.trim().split("\n"),r=t[0].split(",").map(e=>e.trim()),a=[];for(let e=1;e<t.length;e++){let s=t[e].split(",").map(e=>e.trim()),n={};r.forEach((e,t)=>{let r=s[t];if(["price","averageRating","average_rating","value","priceRangeMin","price_range_min","priceRangeMax","price_range_max"].includes(e)){let s=parseFloat(r);n[e]=isNaN(s)?r:s}else if(["numRatings","num_ratings","inventoryCount","inventory_count","userId","user_id","productId","product_id"].includes(e)){let s=parseInt(r,10);n[e]=isNaN(s)?r:s}else n[e]=r});let i=E(n);a.push(i)}switch(s){case"users":return{users:a};case"products":return{products:a};case"interactions":return{interactions:a};case"batch":return{users:a.filter(e=>e.externalId&&!e.productId&&!e.userId),products:a.filter(e=>e.externalId&&e.name&&!e.userId),interactions:a.filter(e=>(e.userId||e.userExternalId)&&(e.productId||e.productExternalId))};default:return{data:a}}};return(0,r.jsxs)(w.lG,{open:t,onOpenChange:n,children:[(0,r.jsx)(w.zM,{asChild:!0,children:s||(0,r.jsxs)(i.Button,{children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})}),(0,r.jsxs)(w.Cf,{className:"sm:max-w-md",children:[(0,r.jsxs)(w.c7,{children:[(0,r.jsx)(w.L3,{children:"Nueva Ingesta de Datos"}),(0,r.jsx)(w.rr,{children:"Sube un archivo CSV o JSON con tus datos de usuarios, productos o interacciones"})]}),v?(0,r.jsxs)("div",{className:"flex flex-col items-center py-6",children:[(0,r.jsx)(C.A,{className:"h-12 w-12 text-green-500 mb-4"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-green-700",children:"\xa1Ingesta iniciada!"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tu archivo est\xe1 siendo procesado"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{htmlFor:"dataType",children:"Tipo de datos"}),(0,r.jsxs)(y.l6,{value:o,onValueChange:e=>l(e),children:[(0,r.jsx)(y.bq,{children:(0,r.jsx)(y.yv,{placeholder:"Selecciona el tipo de datos"})}),(0,r.jsxs)(y.gC,{children:[(0,r.jsx)(y.eb,{value:"batch",children:"Lote completo (usuarios, productos, interacciones)"}),(0,r.jsx)(y.eb,{value:"users",children:"Solo usuarios"}),(0,r.jsx)(y.eb,{value:"products",children:"Solo productos"}),(0,r.jsx)(y.eb,{value:"interactions",children:"Solo interacciones"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{htmlFor:"file",children:"Archivo"}),(0,r.jsx)(N.p,{id:"file",type:"file",accept:".csv,.json,.txt",onChange:e=>{let s=e.target.files?.[0];if(s){let e=s.name.toLowerCase().substr(s.name.lastIndexOf("."));if(!["text/csv","application/json","text/plain"].includes(s.type)&&![".csv",".json",".txt"].includes(e))return void g("Por favor selecciona un archivo CSV o JSON v\xe1lido");if(s.size>0xa00000)return void g("El archivo es demasiado grande. M\xe1ximo 10MB permitido");h(s),g(null)}},disabled:m}),x&&(0,r.jsxs)("div",{className:"mt-2 flex items-center text-sm text-muted-foreground",children:[(0,r.jsx)(S,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:[x.name," (",(x.size/1024).toFixed(1)," KB)"]})]})]}),j&&(0,r.jsxs)(d.Fc,{variant:"destructive",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)(d.TN,{children:j})]}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)("p",{children:"Formatos soportados: CSV, JSON"}),(0,r.jsx)("p",{children:"Tama\xf1o m\xe1ximo: 10MB"})]})]}),(0,r.jsxs)(w.Es,{children:[(0,r.jsx)(i.Button,{variant:"outline",onClick:()=>{m||(n(!1),h(null),g(null),f(!1),l("batch"))},disabled:m,children:"Cancelar"}),!v&&(0,r.jsx)(i.Button,{onClick:_,disabled:!x||m,children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(I.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Subiendo..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Iniciar Ingesta"]})})]})]})]})}var E=t(13668),k=t(58133);function L(){let{jobs:e,isLoading:s,error:t,startBatchIngestion:C}=(0,_.o)(),[S,I]=(0,a.useState)(null),{filteredJobs:L,searchQuery:q,setSearchQuery:z,statusFilter:F,setStatusFilter:M,clearFilters:D}=(0,A.x)(e,(e,s)=>e.jobId.toString().includes(s)||(e.filePath?.toLowerCase().includes(s.toLowerCase())??!1)),J=e=>(0,E.z3)(e),T=e=>"FAILED"===e.status,B=e=>{console.log("Retrying job:",e)},R=e=>{console.log("Downloading file:",e)};return s?(0,r.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-card/50 border border-border/50 rounded-lg p-6",children:[(0,r.jsx)(o.E,{className:"h-8 w-64 mb-2"}),(0,r.jsx)(o.E,{className:"h-4 w-96"})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(o.E,{className:"h-6 w-48"}),(0,r.jsx)(o.E,{className:"h-4 w-32"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(o.E,{className:"h-64 w-full"})})]})]}):(0,r.jsxs)(k.hI,{title:"Historial de Ingesta de Datos",description:"Seguimiento completo de todos tus procesos de carga de datos",actions:(0,r.jsx)(P,{onIngestionStart:C,trigger:(0,r.jsxs)(i.Button,{children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Nueva Ingesta"]})}),children:[(0,r.jsx)(k.os,{title:"Resumen",icon:(0,r.jsx)(x.A,{className:"h-6 w-6 text-green-500"}),children:(0,r.jsxs)("div",{className:"flex gap-4 text-sm text-muted-foreground p-6",children:[(0,r.jsxs)("span",{children:["Total: ",e.length]}),(0,r.jsxs)("span",{children:["Completados: ",e.filter(e=>"COMPLETED"===e.status).length]}),(0,r.jsxs)("span",{children:["En proceso: ",e.filter(e=>"PROCESSING"===e.status).length]}),(0,r.jsxs)("span",{children:["Fallidos: ",e.filter(e=>"FAILED"===e.status).length]})]})}),t&&(0,r.jsxs)(d.Fc,{variant:"destructive",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)(d.XL,{children:"Error"}),(0,r.jsx)(d.TN,{children:t})]}),(0,r.jsx)(k.os,{title:"Filtros",icon:(0,r.jsx)(h.A,{className:"h-5 w-5"}),children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col gap-6 sm:flex-row sm:items-center sm:gap-6",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(N.p,{placeholder:"Buscar por ID o archivo...",value:q,onChange:e=>z(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,r.jsx)(b.J,{htmlFor:"statusFilter",className:"text-sm whitespace-nowrap",children:"Estado:"}),(0,r.jsxs)(y.l6,{value:F,onValueChange:e=>M(e),children:[(0,r.jsx)(y.bq,{className:"w-32",children:(0,r.jsx)(y.yv,{})}),(0,r.jsxs)(y.gC,{children:[(0,r.jsx)(y.eb,{value:"all",children:"Todos"}),(0,r.jsx)(y.eb,{value:"pending",children:"Pendiente"}),(0,r.jsx)(y.eb,{value:"processing",children:"Procesando"}),(0,r.jsx)(y.eb,{value:"completed",children:"Completado"}),(0,r.jsx)(y.eb,{value:"failed",children:"Fallido"})]})]})]}),(0,r.jsx)(i.Button,{variant:"outline",size:"sm",onClick:D,children:"Limpiar"})]})})}),(0,r.jsx)(k.os,{title:"Trabajos de Ingesta",description:"Lista completa de procesos de carga de datos con detalles y estad\xedsticas",children:(0,r.jsx)(n.Wu,{className:"p-0",children:(0,r.jsx)("div",{className:"overflow-hidden",children:(0,r.jsxs)(l.XI,{children:[(0,r.jsx)(l.A0,{className:"bg-muted/10",children:(0,r.jsxs)(l.Hj,{className:"border-b border-border/30",children:[(0,r.jsx)(l.nd,{className:"font-semibold",children:"Job ID"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Estado"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Fecha Inicio"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Duraci\xf3n"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Registros Procesados"}),(0,r.jsx)(l.nd,{className:"font-semibold",children:"Archivo"}),(0,r.jsx)(l.nd,{className:"text-right font-semibold",children:"Acciones"})]})}),(0,r.jsx)(l.BF,{children:L.length>0?L.map((e,s)=>(0,r.jsxs)(k.AP,{index:s,children:[(0,r.jsxs)(l.nA,{className:"font-medium py-4",children:["#",e.jobId]}),(0,r.jsx)(l.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,E.cR)(e.status),(0,E.KC)(e.status)]})}),(0,r.jsx)(l.nA,{className:"py-4",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{children:(0,v.GP)(new Date(e.createdAt),"dd/MM/yyyy",{locale:f.es})}),(0,r.jsx)("div",{className:"text-muted-foreground",children:(0,v.GP)(new Date(e.createdAt),"HH:mm",{locale:f.es})})]})}),(0,r.jsx)(l.nA,{className:"py-4",children:e.duration?(0,r.jsx)("span",{className:"text-sm font-medium",children:(0,E.a3)(e.duration)}):"PROCESSING"===e.status?(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"En curso"}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(l.nA,{className:"py-4",children:e.records_processed?(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{className:"font-medium",children:["Total: ",e.records_processed.total?.toLocaleString()||"—"]}),(0,r.jsxs)("div",{className:"text-muted-foreground text-xs",children:[e.records_processed.users&&`${e.records_processed.users.toLocaleString()} usuarios`,e.records_processed.products&&`, ${e.records_processed.products.toLocaleString()} productos`,e.records_processed.interactions&&`, ${e.records_processed.interactions.toLocaleString()} interacciones`]})]}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(l.nA,{className:"py-4",children:e.filePath?(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium truncate max-w-32",title:e.filePath,children:e.filePath.split("/").pop()}),e.fileSize&&(0,r.jsx)("div",{className:"text-muted-foreground text-xs",children:J(e.fileSize)})]}):(0,r.jsx)("span",{className:"text-muted-foreground",children:"—"})}),(0,r.jsx)(l.nA,{className:"text-right py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,r.jsxs)(w.lG,{children:[(0,r.jsx)(w.zM,{asChild:!0,children:(0,r.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>I(e),className:"h-8 w-8 p-0 hover:bg-muted/50",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(w.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(w.c7,{children:[(0,r.jsxs)(w.L3,{children:["Detalles del Job #",e.jobId]}),(0,r.jsx)(w.rr,{children:"Informaci\xf3n completa del trabajo de ingesta de datos"})]}),S&&(0,r.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Estado"}),(0,r.jsx)("div",{className:"mt-1",children:(0,E.KC)(S.status)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Duraci\xf3n"}),(0,r.jsx)("p",{className:"text-sm",children:S.duration?(0,E.a3)(S.duration):"En curso"})]})]}),S.filePath&&(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Archivo"}),(0,r.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,r.jsx)("code",{className:"text-xs bg-muted p-2 rounded flex-1",children:S.filePath}),(0,r.jsx)(i.Button,{size:"sm",variant:"outline",onClick:()=>R(S.filePath),disabled:!0,children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})]}),S.fileSize&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Tama\xf1o: ",J(S.fileSize)]})]}),S.records_processed&&(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Registros Procesados"}),(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-2 mt-2",children:[S.records_processed.users&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Usuarios"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:S.records_processed.users.toLocaleString()})]}),S.records_processed.products&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Productos"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:S.records_processed.products.toLocaleString()})]}),S.records_processed.interactions&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Interacciones"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:S.records_processed.interactions.toLocaleString()})]}),S.records_processed.total&&(0,r.jsxs)("div",{className:"bg-muted p-2 rounded",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Total"}),(0,r.jsx)("div",{className:"text-sm font-bold",children:S.records_processed.total.toLocaleString()})]})]})]}),S.errorMessage&&(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium text-destructive",children:"Error"}),(0,r.jsxs)(d.Fc,{variant:"destructive",className:"mt-1",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)(d.TN,{className:"text-sm",children:S.errorMessage})]})]}),S.taskId&&(0,r.jsxs)("div",{children:[(0,r.jsx)(b.J,{className:"text-sm font-medium",children:"Task ID"}),(0,r.jsx)("code",{className:"text-xs bg-muted p-1 rounded block mt-1",children:S.taskId})]})]})]})]}),T(e)&&(0,r.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>B(e.jobId),className:"h-8 w-8 p-0 hover:bg-muted/50",disabled:!0,children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})})]},e.jobId)):(0,r.jsx)(k.AP,{index:0,children:(0,r.jsx)(l.nA,{colSpan:7,className:"text-center py-8",children:(0,r.jsx)("div",{className:"flex flex-col items-center gap-2 text-muted-foreground",children:0===e.length?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"h-8 w-8"}),(0,r.jsx)("p",{children:"No hay trabajos de ingesta a\xfan"}),(0,r.jsx)("p",{className:"text-sm",children:"Los trabajos aparecer\xe1n aqu\xed cuando subas datos"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-8 w-8"}),(0,r.jsx)("p",{children:"No se encontraron trabajos con los filtros aplicados"}),(0,r.jsx)(i.Button,{variant:"outline",size:"sm",onClick:D,children:"Limpiar filtros"})]})})})})})]})})})}),(0,r.jsxs)(d.Fc,{children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)(d.XL,{children:"Informaci\xf3n sobre ingesta de datos"}),(0,r.jsx)(d.TN,{children:(0,r.jsxs)("div",{className:"space-y-2 text-sm mt-2",children:[(0,r.jsx)("p",{children:"Los trabajos de ingesta procesan archivos de datos (CSV, JSON) para actualizar usuarios, productos e interacciones en el sistema."}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1 pl-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Formatos soportados:"})," CSV, JSON"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Tipos de datos:"})," Usuarios, Productos, Interacciones"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Validaci\xf3n:"})," Se valida formato y campos obligatorios"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Procesamiento:"})," Los datos se procesan de forma as\xedncrona"]})]})]})})]})]})}},67428:(e,s,t)=>{Promise.resolve().then(t.bind(t,64680))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85988:(e,s,t)=>{Promise.resolve().then(t.bind(t,53697))},89571:(e,s,t)=>{"use strict";t.d(s,{o:()=>n});var r=t(43210),a=t(81184);function n(){let[e,s]=(0,r.useState)([]),[t,n]=(0,r.useState)(!0),[i,o]=(0,r.useState)(null),l=async()=>{try{n(!0),o(null);try{let e=(await (0,a._C)().listBatchJobsApiV1IngestionBatchGet()).data.map(e=>{let s={...e,status:e.status.toUpperCase(),records_processed:e.processedCount?{users:e.processedCount.users,products:e.processedCount.products,interactions:e.processedCount.interactions,total:e.processedCount.total}:void 0};if(s.startedAt&&s.completedAt){let e=new Date(s.startedAt).getTime(),t=new Date(s.completedAt).getTime();s.duration=Math.round((t-e)/1e3)}return s});s(e);return}catch(e){o("Error fetching ingestion jobs"),console.error("Error fetching ingestion jobs:",e)}}catch(e){o(e instanceof Error?e.message:"Error loading ingestion jobs"),console.error("Error loading ingestion jobs:",e)}finally{n(!1)}},d=async e=>{try{let s=await (0,a._C)().batchDataIngestionApiV1IngestionBatchPost(e);return await l(),s.data}catch(e){throw console.error("Error starting batch ingestion:",e),e}};return{jobs:e,isLoading:t,error:i,fetchJobs:l,getJobStatus:async e=>{try{return(await (0,a._C)().getBatchJobStatusApiV1IngestionBatchJobIdGet(e)).data}catch(e){throw console.error("Error fetching job status:",e),e}},startBatchIngestion:d}}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,2713,5814,5423,1576,7400,6920,2807,5320,3302],()=>t(51011));module.exports=r})();