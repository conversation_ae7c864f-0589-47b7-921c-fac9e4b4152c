from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from .base import CamelCaseModel


class EndUserBase(CamelCaseModel):
    external_id: str


class EndUserCreate(EndUserBase):
    # Cold start preference fields
    preferred_categories: Optional[List[str]] = Field(
        None,
        description="List of preferred product categories for initial recommendations"
    )
    disliked_categories: Optional[List[str]] = Field(
        None,
        description="List of categories to avoid in recommendations"
    )
    preferred_brands: Optional[List[str]] = Field(
        None,
        description="List of preferred brands for initial recommendations"
    )
    disliked_brands: Optional[List[str]] = Field(
        None,
        description="List of brands to avoid in recommendations"
    )
    price_range_min: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Minimum price preference for recommendations"
    )
    price_range_max: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Maximum price preference for recommendations"
    )
    demographic_info: Optional[Dict[str, Any]] = Field(
        None,
        description="Demographic information (age_group, gender, location, etc.)"
    )
    onboarding_preferences: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional preferences collected during onboarding"
    )


class EndUserUpdate(BaseModel):
    external_id: Optional[str] = None
    is_active: Optional[bool] = None
    preferred_categories: Optional[List[str]] = None
    disliked_categories: Optional[List[str]] = None
    preferred_brands: Optional[List[str]] = None
    disliked_brands: Optional[List[str]] = None
    price_range_min: Optional[Decimal] = Field(None, ge=0)
    price_range_max: Optional[Decimal] = Field(None, ge=0)
    demographic_info: Optional[Dict[str, Any]] = None
    onboarding_preferences: Optional[Dict[str, Any]] = None


class EndUser(EndUserBase):
    account_id: int
    user_id: int  # Fixed: Use user_id to match database model
    created_at: datetime
    updated_at: datetime
    is_active: bool
    deleted_at: Optional[datetime] = None

    # Include preference fields in response
    preferred_categories: Optional[List[str]] = None
    disliked_categories: Optional[List[str]] = None
    preferred_brands: Optional[List[str]] = None
    disliked_brands: Optional[List[str]] = None
    price_range_min: Optional[Decimal] = None
    price_range_max: Optional[Decimal] = None
    demographic_info: Optional[Dict[str, Any]] = None
    onboarding_preferences: Optional[Dict[str, Any]] = None

    class ConfigDict:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "external_id": "user_12345",
                "preferred_categories": ["electronics", "books", "clothing"],
                "disliked_categories": ["sports"],
                "preferred_brands": ["Apple", "Nike", "Samsung"],
                "disliked_brands": ["BrandX"],
                "price_range_min": 10,
                "price_range_max": 500,
                "demographic_info": {
                    "age_group": "25-34",
                    "gender": "female",
                    "location": "urban",
                    "income_level": "middle"
                },
                "onboarding_preferences": {
                    "shopping_frequency": "weekly",
                    "preferred_shopping_time": "evening",
                    "interests": ["technology", "fashion", "reading"],
                    "lifestyle": "active"
                }
            }
        }
