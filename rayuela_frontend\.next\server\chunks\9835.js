"use strict";exports.id=9835,exports.ids=[9835],exports.modules={10510:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},16825:(e,a,t)=>{t.d(a,{Vd:()=>s,qC:()=>c,_X:()=>E,Io:()=>d});var i=t(43210),r=t.n(i);let o=[{id:"improve-precision",metricPath:["summary","precision"],threshold:.5,comparison:"lt",title:"METRIC_REC_PRECISION_TITLE",description:"METRIC_REC_PRECISION_DESC",priority:"high",category:"accuracy",iconKey:"BarChart2Icon",actions:["METRIC_REC_PRECISION_ACTION_1","METRIC_REC_PRECISION_ACTION_2","METRIC_REC_PRECISION_ACTION_3"],metrics:[{name:"METRIC_PRECISION",valuePath:["summary","precision"],valueMultiplier:100,target:50,unit:"%"}]},{id:"improve-diversity",metricPath:["summary","diversity"],threshold:.4,comparison:"lt",title:"METRIC_REC_DIVERSITY_TITLE",description:"METRIC_REC_DIVERSITY_DESC",priority:"medium",category:"diversity",iconKey:"ShuffleIcon",actions:["METRIC_REC_DIVERSITY_ACTION_1","METRIC_REC_DIVERSITY_ACTION_2","METRIC_REC_DIVERSITY_ACTION_3","METRIC_REC_DIVERSITY_ACTION_4"],metrics:[{name:"METRIC_DIVERSITY",valuePath:["summary","diversity"],valueMultiplier:100,target:40,unit:"%"},{name:"METRIC_CATALOG_COVERAGE",valuePath:["summary","catalog_coverage"],valueMultiplier:100,target:30,unit:"%"}]},{id:"improve-novelty",metricPath:["summary","novelty"],threshold:.3,comparison:"lt",title:"METRIC_REC_NOVELTY_TITLE",description:"METRIC_REC_NOVELTY_DESC",priority:"medium",category:"diversity",iconKey:"ZapIcon",actions:["METRIC_REC_NOVELTY_ACTION_1","METRIC_REC_NOVELTY_ACTION_2","METRIC_REC_NOVELTY_ACTION_3","METRIC_REC_NOVELTY_ACTION_4"],metrics:[{name:"METRIC_NOVELTY",valuePath:["summary","novelty"],valueMultiplier:100,target:30,unit:"%"}]},{id:"improve-ranking",metricPath:["summary","ndcg"],threshold:.45,comparison:"lt",title:"METRIC_REC_RANKING_TITLE",description:"METRIC_REC_RANKING_DESC",priority:"high",category:"accuracy",iconKey:"BarChart2Icon",actions:["METRIC_REC_RANKING_ACTION_1","METRIC_REC_RANKING_ACTION_2","METRIC_REC_RANKING_ACTION_3","METRIC_REC_RANKING_ACTION_4"],metrics:[{name:"METRIC_NDCG",valuePath:["summary","ndcg"],valueMultiplier:100,target:45,unit:"%"}]},{id:"improve-serendipity",metricPath:["summary","serendipity"],threshold:.25,comparison:"lt",title:"METRIC_REC_SERENDIPITY_TITLE",description:"METRIC_REC_SERENDIPITY_DESC",priority:"low",category:"diversity",iconKey:"LightbulbIcon",actions:["METRIC_REC_SERENDIPITY_ACTION_1","METRIC_REC_SERENDIPITY_ACTION_2","METRIC_REC_SERENDIPITY_ACTION_3","METRIC_REC_SERENDIPITY_ACTION_4"],metrics:[{name:"METRIC_SERENDIPITY",valuePath:["summary","serendipity"],valueMultiplier:100,target:25,unit:"%"}]},{id:"improve-confidence",specialLogic:{type:"avgConfidence",params:{threshold:.6,paths:[["confidence_distribution","collaborative","avg"],["confidence_distribution","content","avg"],["confidence_distribution","hybrid","avg"]]}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_CONFIDENCE_TITLE",description:"METRIC_REC_CONFIDENCE_DESC",priority:"high",category:"confidence",iconKey:"AlertCircleIcon",actions:["METRIC_REC_CONFIDENCE_ACTION_1","METRIC_REC_CONFIDENCE_ACTION_2","METRIC_REC_CONFIDENCE_ACTION_3","METRIC_REC_CONFIDENCE_ACTION_4"],metrics:[{name:"METRIC_AVG_CONFIDENCE",valuePath:[],valueMultiplier:100,target:60,unit:"%"}]},{id:"improve-model-type",specialLogic:{type:"worstModel",params:{threshold:.5,models:{collaborative:{path:["confidence_distribution","collaborative","avg"],name:"METRIC_MODEL_COLLABORATIVE",actions:["METRIC_REC_MODEL_COLLAB_ACTION_1","METRIC_REC_MODEL_COLLAB_ACTION_2","METRIC_REC_MODEL_COLLAB_ACTION_3","METRIC_REC_MODEL_COLLAB_ACTION_4"]},content:{path:["confidence_distribution","content","avg"],name:"METRIC_MODEL_CONTENT",actions:["METRIC_REC_MODEL_CONTENT_ACTION_1","METRIC_REC_MODEL_CONTENT_ACTION_2","METRIC_REC_MODEL_CONTENT_ACTION_3","METRIC_REC_MODEL_CONTENT_ACTION_4"]},hybrid:{path:["confidence_distribution","hybrid","avg"],name:"METRIC_MODEL_HYBRID",actions:["METRIC_REC_MODEL_HYBRID_ACTION_1","METRIC_REC_MODEL_HYBRID_ACTION_2","METRIC_REC_MODEL_HYBRID_ACTION_3","METRIC_REC_MODEL_HYBRID_ACTION_4"]}}}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_MODEL_TITLE",description:"METRIC_REC_MODEL_DESC",priority:"medium",category:"confidence",iconKey:"DatabaseIcon",actions:[],metrics:[{name:"METRIC_MODEL_CONFIDENCE",valuePath:[],valueMultiplier:100,target:50,unit:"%"}]},{id:"improve-low-confidence-categories",specialLogic:{type:"lowConfidenceCategories",params:{threshold:.5,maxCategories:3}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_CATEGORIES_TITLE",description:"METRIC_REC_CATEGORIES_DESC",priority:"medium",category:"confidence",iconKey:"DatabaseIcon",actions:["METRIC_REC_CATEGORIES_ACTION_1","METRIC_REC_CATEGORIES_ACTION_2","METRIC_REC_CATEGORIES_ACTION_3","METRIC_REC_CATEGORIES_ACTION_4"],metrics:[]},{id:"improve-confidence-factors",specialLogic:{type:"lowestConfidenceFactor",params:{threshold:.4,factors:{user_history_size:{name:"METRIC_FACTOR_USER_HISTORY",actions:["METRIC_REC_FACTOR_USER_HISTORY_ACTION_1","METRIC_REC_FACTOR_USER_HISTORY_ACTION_2","METRIC_REC_FACTOR_USER_HISTORY_ACTION_3","METRIC_REC_FACTOR_USER_HISTORY_ACTION_4"]},item_popularity:{name:"METRIC_FACTOR_ITEM_POPULARITY",actions:["METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3","METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4"]},category_strength:{name:"METRIC_FACTOR_CATEGORY_STRENGTH",actions:["METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3","METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4"]},model_type:{name:"METRIC_FACTOR_MODEL_TYPE",actions:["METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3","METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4"]}}}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_FACTOR_TITLE",description:"METRIC_REC_FACTOR_DESC",priority:"medium",category:"confidence",iconKey:"UsersIcon",actions:[],metrics:[{name:"METRIC_FACTOR",valuePath:[],valueMultiplier:100,target:40,unit:"%"}]},{id:"address-negative-trend",specialLogic:{type:"negativeTrend",params:{minDays:3,threshold:.9,path:["confidence_trends","last_7_days"]}},metricPath:[],threshold:0,comparison:"lt",title:"METRIC_REC_TREND_TITLE",description:"METRIC_REC_TREND_DESC",priority:"high",category:"confidence",iconKey:"TrendingUpIcon",actions:["METRIC_REC_TREND_ACTION_1","METRIC_REC_TREND_ACTION_2","METRIC_REC_TREND_ACTION_3","METRIC_REC_TREND_ACTION_4"],metrics:[{name:"METRIC_CONFIDENCE_CHANGE",valuePath:[],valueMultiplier:100,target:0,unit:"%"}]}];var n=t(66420);function _(e){return n.Nn[e]??e}function C(e,a){return a.reduce((e,a)=>e&&"object"==typeof e&&null!==e&&a in e?e[a]:void 0,e)}function E(e,a,t){let i=[];o.forEach(o=>{if(o.specialLogic){let e=function(e,a,t,i){if(!e.specialLogic)return null;let{type:o,params:E}=e.specialLogic;switch(o){case"avgConfidence":{if(!E?.paths||!E.threshold)return null;let a=E.paths.map(e=>C(t,e)||0),o=a.reduce((e,a)=>e+a,0)/a.length;if(o<E.threshold)return{id:e.id,title:(0,n.JT)(_(e.title),{}),description:(0,n.JT)(_(e.description),{}),priority:e.priority,category:e.category,icon:r().createElement(i[e.iconKey],{className:"h-5 w-5 text-red-500"}),actions:e.actions.map(e=>_(e)),metrics:e.metrics.map(e=>({name:(0,n.JT)(_(e.name),{}),value:o*(e.valueMultiplier||1),target:e.target,unit:e.unit}))};return null}case"worstModel":{if(!E?.models||!E.threshold)return null;let a={};Object.entries(E.models).forEach(([e,i])=>{let r=C(t,i.path);a[e]={value:"number"==typeof r?r:0,name:n.Nn[i.name],actions:i.actions}});let o=Object.entries(a).sort(([,e],[,a])=>e.value-a.value)[0];if(o&&o[1].value<E.threshold){let a=o[1].name;return{id:`${e.id}-${o[0]}`,title:(0,n.JT)(n.Nn[e.title],{model:a}),description:(0,n.JT)(n.Nn[e.description],{model:a}),priority:e.priority,category:e.category,icon:r().createElement(i[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:o[1].actions.map(e=>n.Nn[e]),metrics:e.metrics.map(e=>({name:(0,n.JT)(n.Nn[e.name],{model:a}),value:o[1].value*(e.valueMultiplier||1),target:e.target,unit:e.unit}))}}return null}case"lowConfidenceCategories":{if(!E?.threshold||!E.maxCategories||0===Object.keys(t.category_confidence).length)return null;let a=Object.entries(t.category_confidence).sort(([,e],[,a])=>e-a).slice(0,E.maxCategories);if(a[0][1]<E.threshold){let t=a[0][0];return{id:e.id,title:(0,n.JT)(n.Nn[e.title],{}),description:(0,n.JT)(n.Nn[e.description],{category:t}),priority:e.priority,category:e.category,icon:r().createElement(i[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:e.actions.map(e=>(0,n.JT)(n.Nn[e],{category:t})),metrics:a.map(e=>({name:`Confianza en ${e[0]}`,value:100*e[1],target:100*E.threshold,unit:"%"}))}}return null}case"lowestConfidenceFactor":{if(!E?.factors||!E.threshold)return null;let a=Object.entries(t.confidence_factors).sort(([,e],[,a])=>e-a)[0];if(a[1]<E.threshold){let t=a[0],o=E.factors[t];if(!o)return null;let C=n.Nn[o.name];return{id:`${e.id}-${t}`,title:(0,n.JT)(_(e.title),{factor:C}),description:(0,n.JT)(_(e.description),{factor:C}),priority:e.priority,category:e.category,icon:r().createElement(i[e.iconKey],{className:"h-5 w-5 text-amber-500"}),actions:o.actions.map(e=>_(e)),metrics:e.metrics.map(e=>({name:(0,n.JT)(_(e.name),{factor:C}),value:a[1]*(e.valueMultiplier||1),target:e.target,unit:e.unit}))}}return null}case"negativeTrend":{if(!E?.minDays||!E.threshold||!E.path)return null;let a=C(t,E.path);if(a&&Array.isArray(a)&&a.length>=E.minDays){let t=a.slice(-E.minDays);if(t[t.length-1]?.avg_confidence<t[0]?.avg_confidence*E.threshold){let a=(t[t.length-1].avg_confidence/t[0].avg_confidence-1)*100;return{id:e.id,title:(0,n.JT)(_(e.title),{}),description:(0,n.JT)(_(e.description),{}),priority:e.priority,category:e.category,icon:r().createElement(i[e.iconKey],{className:"h-5 w-5 text-red-500"}),actions:e.actions.map(e=>_(e)),metrics:e.metrics.map(e=>({name:(0,n.JT)(_(e.name),{}),value:a,target:e.target,unit:e.unit}))}}}return null}default:return null}}(o,0,a,t);e&&i.push(e);return}let E=C("summary"===o.metricPath[0]?e:a,o.metricPath);void 0!==E&&"number"==typeof E&&function(e,a,t){switch(t){case"lt":return e<a;case"gt":return e>a;case"eq":return e===a;case"lte":return e<=a;case"gte":return e>=a;default:return!1}}(E,o.threshold,o.comparison)&&i.push({id:o.id,title:(0,n.JT)(_(o.title),{}),description:(0,n.JT)(_(o.description),{}),priority:o.priority,category:o.category,icon:r().createElement(t[o.iconKey],{className:`h-5 w-5 ${"high"===o.priority?"text-red-500":"medium"===o.priority?"text-amber-500":"text-blue-500"}`}),actions:o.actions.map(e=>_(e)),metrics:o.metrics.map(t=>{let i=C("summary"===t.valuePath[0]?e:a,t.valuePath);return{name:(0,n.JT)(_(t.name),{}),value:i*(t.valueMultiplier||1),target:t.target,unit:t.unit}})})});let E={high:0,medium:1,low:2};return i.sort((e,a)=>E[e.priority]-E[a.priority]),i}function s(e,a,t,i){let r=!!i&&!!i,o="true"===localStorage.getItem("seenPostModalHighlight"),n=!1;if(i&&i.created_at&&("string"==typeof i.created_at||"number"==typeof i.created_at)){let e=new Date(i.created_at);n=(new Date().getTime()-e.getTime())/36e5<24}let _=a.storage_used??a.storage?.usedBytes??0,C=_>0,E=_>0,s=!!a.training?.lastTrainingDate,c=(a.api_calls_count??a.apiCalls?.used??0)>0;return{updatedItems:t.map(e=>"generate_key"===e.id&&e.autoDetect?{...e,completed:r||o}:"send_catalog_data"===e.id&&e.autoDetect?{...e,completed:C}:"send_interaction_data"===e.id&&e.autoDetect?{...e,completed:E}:"train_model"===e.id&&e.autoDetect?{...e,completed:s}:"first_recommendation"===e.id&&e.autoDetect?{...e,completed:c}:e),isNewApiKey:n,hasApiKey:r,hasSentCatalogData:C,hasSentInteractionData:E,hasTrainedModel:s,hasMadeApiCalls:c}}function c(e,a,t,i,r,o,n){let _={};return e.forEach(e=>{"generate_key"===e.id&&e.autoDetect?_[e.id]=a||t:"send_catalog_data"===e.id&&e.autoDetect?_[e.id]=i:"send_interaction_data"===e.id&&e.autoDetect?_[e.id]=r:"train_model"===e.id&&e.autoDetect?_[e.id]=o:"first_recommendation"===e.id&&e.autoDetect?_[e.id]=n:_[e.id]=e.completed}),_}function d(e,a){return e&&!localStorage.getItem("checklistHighlighted")}},19959:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},25334:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},25541:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("trending-up",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},32192:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},41550:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},56748:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("compass",[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},58559:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},58887:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},66420:(e,a,t)=>{t.d(a,{JT:()=>r,Nn:()=>i});let i={COMPONENT_TITLE:"Recomendaciones Inteligentes",COMPONENT_DESCRIPTION:"Basadas en el an\xe1lisis de {count} \xe1reas de mejora potencial",LOADING_TEXT:"Analizando m\xe9tricas para generar recomendaciones...",OPTIMIZED_TITLE:"Sistema Optimizado",OPTIMIZED_DESCRIPTION:"No se han detectado \xe1reas de mejora significativas en este momento.",OPTIMIZED_MESSAGE:"Las m\xe9tricas actuales indican que su sistema de recomendaci\xf3n est\xe1 funcionando de manera \xf3ptima. Contin\xfae monitoreando las m\xe9tricas para mantener este rendimiento.",RECOMMENDATION_COUNT:"{count} {count, plural, one {recomendaci\xf3n} other {recomendaciones}}",ACTIONS_TITLE:"Acciones recomendadas:",CATEGORY_ACCURACY:"Precisi\xf3n y Relevancia",CATEGORY_DIVERSITY:"Diversidad y Descubrimiento",CATEGORY_CONFIDENCE:"Confianza y Fiabilidad",CATEGORY_PERFORMANCE:"Rendimiento y Eficiencia",METRIC_PRECISION:"Precisi\xf3n",METRIC_DIVERSITY:"Diversidad",METRIC_CATALOG_COVERAGE:"Cobertura del Cat\xe1logo",METRIC_NOVELTY:"Novedad",METRIC_NDCG:"NDCG",METRIC_SERENDIPITY:"Serendipia",METRIC_AVG_CONFIDENCE:"Confianza Promedio",METRIC_CONFIDENCE_CHANGE:"Cambio en confianza",METRIC_TARGET:"Meta: {value}{unit}",METRIC_MODEL_COLLABORATIVE:"colaborativo",METRIC_MODEL_CONTENT:"basado en contenido",METRIC_MODEL_HYBRID:"h\xedbrido",METRIC_MODEL_CONFIDENCE:"Confianza {model}",METRIC_FACTOR_USER_HISTORY:"tama\xf1o del historial de usuario",METRIC_FACTOR_ITEM_POPULARITY:"popularidad del \xedtem",METRIC_FACTOR_CATEGORY_STRENGTH:"fuerza de categor\xeda",METRIC_FACTOR_MODEL_TYPE:"tipo de modelo",METRIC_FACTOR:"Factor {factor}",METRIC_REC_PRECISION_TITLE:"Mejorar la precisi\xf3n de las recomendaciones",METRIC_REC_DIVERSITY_TITLE:"Aumentar la diversidad de recomendaciones",METRIC_REC_NOVELTY_TITLE:"Incrementar la novedad de las recomendaciones",METRIC_REC_RANKING_TITLE:"Mejorar la calidad del ranking de recomendaciones",METRIC_REC_SERENDIPITY_TITLE:"Aumentar la serendipia en las recomendaciones",METRIC_REC_CONFIDENCE_TITLE:"Aumentar la confianza en las recomendaciones",METRIC_REC_MODEL_TITLE:"Mejorar el rendimiento del modelo {model}",METRIC_REC_CATEGORIES_TITLE:"Mejorar recomendaciones en categor\xedas de baja confianza",METRIC_REC_FACTOR_TITLE:"Mejorar el factor de {factor}",METRIC_REC_TREND_TITLE:"Abordar tendencia negativa en confianza",METRIC_REC_PRECISION_DESC:"La precisi\xf3n actual est\xe1 por debajo del umbral recomendado del 50%. Considere ajustar los modelos para mejorar la relevancia de las recomendaciones.",METRIC_REC_DIVERSITY_DESC:"Las recomendaciones actuales muestran poca diversidad, lo que puede llevar a una experiencia mon\xf3tona para los usuarios.",METRIC_REC_NOVELTY_DESC:"El sistema tiende a recomendar \xedtems muy populares, limitando el descubrimiento de nuevos productos.",METRIC_REC_RANKING_DESC:"El NDCG actual indica que el orden de las recomendaciones podr\xeda no ser \xf3ptimo, afectando la experiencia del usuario.",METRIC_REC_SERENDIPITY_DESC:"Las recomendaciones actuales podr\xedan ser demasiado predecibles, limitando el descubrimiento de \xedtems inesperados pero relevantes.",METRIC_REC_CONFIDENCE_DESC:"El nivel de confianza promedio est\xe1 por debajo del umbral recomendado del 60%, lo que puede indicar incertidumbre en las predicciones.",METRIC_REC_MODEL_DESC:"El modelo {model} muestra un nivel de confianza bajo, lo que reduce la efectividad general del sistema.",METRIC_REC_CATEGORIES_DESC:'Algunas categor\xedas de productos muestran niveles de confianza particularmente bajos, especialmente "{category}".',METRIC_REC_FACTOR_DESC:"El factor de {factor} tiene una contribuci\xf3n baja a la confianza general, lo que indica un \xe1rea de mejora.",METRIC_REC_TREND_DESC:"La confianza promedio ha disminuido significativamente en los \xfaltimos d\xedas, lo que podr\xeda indicar un problema emergente.",METRIC_REC_PRECISION_ACTION_1:"Ajustar los par\xe1metros del modelo colaborativo para dar m\xe1s peso a interacciones recientes",METRIC_REC_PRECISION_ACTION_2:"Aumentar el tama\xf1o del conjunto de entrenamiento con m\xe1s datos de interacciones",METRIC_REC_PRECISION_ACTION_3:"Implementar t\xe9cnicas de filtrado para eliminar outliers en los datos de entrenamiento",METRIC_REC_DIVERSITY_ACTION_1:"Implementar un algoritmo de re-ranking para diversificar los resultados",METRIC_REC_DIVERSITY_ACTION_2:"Ajustar los par\xe1metros del modelo para reducir la concentraci\xf3n en \xedtems populares",METRIC_REC_DIVERSITY_ACTION_3:"Introducir un factor de aleatoriedad controlada en las recomendaciones finales",METRIC_REC_DIVERSITY_ACTION_4:"Considerar categor\xedas menos representadas en las recomendaciones",METRIC_REC_NOVELTY_ACTION_1:"Ajustar el algoritmo para dar m\xe1s peso a \xedtems menos populares",METRIC_REC_NOVELTY_ACTION_2:"Implementar un factor de penalizaci\xf3n para \xedtems extremadamente populares",METRIC_REC_NOVELTY_ACTION_3:'Crear un segmento espec\xedfico de "descubrimientos" con \xedtems de baja popularidad pero alta relevancia',METRIC_REC_NOVELTY_ACTION_4:"Considerar t\xe9cnicas de filtrado colaborativo basadas en vecindad para encontrar \xedtems nicho",METRIC_REC_RANKING_ACTION_1:"Implementar o mejorar algoritmos de Learning-to-Rank",METRIC_REC_RANKING_ACTION_2:"Ajustar los factores de relevancia en el c\xe1lculo del ranking",METRIC_REC_RANKING_ACTION_3:"Considerar se\xf1ales adicionales como recencia o tendencia para el ranking",METRIC_REC_RANKING_ACTION_4:"Experimentar con diferentes funciones de p\xe9rdida optimizadas para NDCG",METRIC_REC_SERENDIPITY_ACTION_1:"Implementar un componente de serendipia que ocasionalmente introduzca \xedtems inesperados",METRIC_REC_SERENDIPITY_ACTION_2:"Explorar conexiones no obvias entre preferencias de usuario y productos",METRIC_REC_SERENDIPITY_ACTION_3:"Considerar t\xe9cnicas de recomendaci\xf3n basadas en conocimiento para descubrir relaciones no evidentes",METRIC_REC_SERENDIPITY_ACTION_4:"Experimentar con modelos de grafos para encontrar conexiones de segundo o tercer grado",METRIC_REC_CONFIDENCE_ACTION_1:"Recopilar m\xe1s datos de interacciones para mejorar la base de predicciones",METRIC_REC_CONFIDENCE_ACTION_2:"Ajustar los umbrales de confianza para filtrar recomendaciones de baja calidad",METRIC_REC_CONFIDENCE_ACTION_3:"Implementar t\xe9cnicas de ensemble para combinar m\xfaltiples modelos",METRIC_REC_CONFIDENCE_ACTION_4:"Mejorar la calidad de los metadatos de productos para fortalecer el modelo basado en contenido",METRIC_REC_MODEL_COLLAB_ACTION_1:"Ajustar los par\xe1metros de similitud entre usuarios/\xedtems",METRIC_REC_MODEL_COLLAB_ACTION_2:"Implementar t\xe9cnicas de factorizaci\xf3n matricial m\xe1s avanzadas",METRIC_REC_MODEL_COLLAB_ACTION_3:"Aumentar el n\xfamero de vecinos considerados en el algoritmo KNN",METRIC_REC_MODEL_COLLAB_ACTION_4:"Reducir el umbral de filtrado para interacciones m\xednimas",METRIC_REC_MODEL_CONTENT_ACTION_1:"Mejorar la calidad y cantidad de atributos de los productos",METRIC_REC_MODEL_CONTENT_ACTION_2:"Implementar t\xe9cnicas de procesamiento de lenguaje natural m\xe1s avanzadas",METRIC_REC_MODEL_CONTENT_ACTION_3:"Ajustar los pesos de los diferentes atributos en el c\xe1lculo de similitud",METRIC_REC_MODEL_CONTENT_ACTION_4:"Considerar la incorporaci\xf3n de embeddings pre-entrenados para representar productos",METRIC_REC_MODEL_HYBRID_ACTION_1:"Ajustar los pesos relativos de los modelos colaborativo y basado en contenido",METRIC_REC_MODEL_HYBRID_ACTION_2:"Implementar un meta-modelo para seleccionar din\xe1micamente el mejor enfoque",METRIC_REC_MODEL_HYBRID_ACTION_3:"Considerar factores contextuales adicionales en la combinaci\xf3n de modelos",METRIC_REC_MODEL_HYBRID_ACTION_4:"Experimentar con diferentes estrategias de ensemble",METRIC_REC_CATEGORIES_ACTION_1:'Recopilar m\xe1s datos de interacciones para la categor\xeda "{category}"',METRIC_REC_CATEGORIES_ACTION_2:"Crear modelos espec\xedficos por categor\xeda para las categor\xedas problem\xe1ticas",METRIC_REC_CATEGORIES_ACTION_3:"Mejorar los metadatos de productos en estas categor\xedas",METRIC_REC_CATEGORIES_ACTION_4:"Considerar reglas de negocio espec\xedficas para complementar el algoritmo",METRIC_REC_FACTOR_USER_HISTORY_ACTION_1:"Implementar estrategias para aumentar la recopilaci\xf3n de interacciones de usuarios",METRIC_REC_FACTOR_USER_HISTORY_ACTION_2:"Mejorar el onboarding para capturar preferencias iniciales",METRIC_REC_FACTOR_USER_HISTORY_ACTION_3:"Considerar t\xe9cnicas de cold-start para usuarios con poco historial",METRIC_REC_FACTOR_USER_HISTORY_ACTION_4:"Implementar recomendaciones basadas en sesi\xf3n para usuarios nuevos",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1:"Balancear mejor las recomendaciones entre \xedtems populares y de nicho",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2:"Implementar un sistema de boosting temporal para nuevos productos",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3:"Crear segmentos de recomendaci\xf3n espec\xedficos para diferentes niveles de popularidad",METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4:"Mejorar la estrategia de exploraci\xf3n vs. explotaci\xf3n",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1:"Mejorar la taxonom\xeda de categor\xedas para capturar mejor las preferencias",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2:"Implementar an\xe1lisis de afinidad de categor\xeda m\xe1s sofisticado",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3:"Considerar la jerarqu\xeda completa de categor\xedas en el c\xe1lculo de afinidad",METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4:"Crear modelos espec\xedficos para categor\xedas principales",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1:"Experimentar con diferentes arquitecturas de modelos",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2:"Implementar un sistema de selecci\xf3n din\xe1mica de modelos basado en contexto",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3:"Considerar modelos m\xe1s avanzados como deep learning para recomendaciones",METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4:"Mejorar la estrategia de ensemble para combinar modelos",METRIC_REC_TREND_ACTION_1:"Investigar cambios recientes en datos o modelos que podr\xedan haber afectado la confianza",METRIC_REC_TREND_ACTION_2:"Verificar la calidad de las interacciones recientes",METRIC_REC_TREND_ACTION_3:"Considerar un rollback a una versi\xf3n anterior del modelo si la tendencia persiste",METRIC_REC_TREND_ACTION_4:"Implementar monitoreo en tiempo real para detectar cambios abruptos en m\xe9tricas clave"};function r(e,a){return e.replace(/{([^}]+)}/g,(e,t)=>{let i=t.match(/^([^,]+),\s*plural,\s*one\s*{([^}]+)}\s*other\s*{([^}]+)}$/);if(i){let e=i[1].trim(),t=i[2].trim(),r=i[3].trim();return 1===a[e]?t:r}return void 0!==a[t]?a[t]:e})}},76242:(e,a,t)=>{t.d(a,{Bc:()=>n,ZI:()=>E,k$:()=>C,m_:()=>_});var i=t(60687);t(43210);var r=t(46442),o=t(4780);function n({delayDuration:e=0,...a}){return(0,i.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...a})}function _({...e}){return(0,i.jsx)(n,{children:(0,i.jsx)(r.bL,{"data-slot":"tooltip",...e})})}function C({...e}){return(0,i.jsx)(r.l9,{"data-slot":"tooltip-trigger",...e})}function E({className:e,sideOffset:a=0,children:t,...n}){return(0,i.jsx)(r.ZL,{children:(0,i.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,o.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...n,children:[t,(0,i.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},78200:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},82080:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},85778:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])}};