(()=>{var e={};e.id=7371,e.ids=[7371],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3563:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,29523))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(75986),a=r(8974);function i(...e){return(0,a.QP)((0,s.$)(e))}},12412:e=>{"use strict";e.exports=require("assert")},14350:(e,t,r)=>{"use strict";r.d(t,{X:()=>o,u:()=>n});let s=process.env.NEXT_PUBLIC_SITE_URL||"https://rayuela.ai",a="Rayuela.ai",i="Sistemas de recomendaci\xf3n avanzados para tu negocio, sin la complejidad de construirlos desde cero.";function o({title:e,description:t=i,path:r,keywords:o=[],type:n="website",publishedTime:d,modifiedTime:c,noIndex:l=!1}){let u=e.includes(a)?e:`${e} | ${a}`,p=`${s}${r}`,h={title:u,description:t,keywords:o.join(", "),authors:[{name:"Rayuela Team"}],creator:"Rayuela",publisher:"Rayuela",robots:l?"noindex, nofollow":"index, follow",alternates:{canonical:p},openGraph:{title:u,description:t,url:p,siteName:a,type:n,locale:"es_AR",images:[{url:`${s}/og-image.png`,width:1200,height:630,alt:e}]},twitter:{card:"summary_large_image",title:u,description:t,images:[`${s}/og-image.png`],creator:"@rayuela_ai"}};return d&&(h.openGraph={...h.openGraph,type:"article",publishedTime:d}),c&&(h.openGraph={...h.openGraph,type:"article",modifiedTime:c}),h}function n(e,t){let r={"@context":"https://schema.org","@type":e};switch(e){case"Organization":return{...r,name:"Rayuela",url:s,logo:`${s}/logo.png`,description:i,foundingDate:"2024",industry:"Software",sameAs:["https://twitter.com/rayuela_ai","https://linkedin.com/company/rayuela-ai"],...t};case"SoftwareApplication":return{...r,name:"Rayuela API",applicationCategory:"DeveloperApplication",operatingSystem:"Any",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",description:"Free tier available"},...t};case"APIReference":return{...r,name:t.name||"Rayuela API Documentation",description:t.description||"Complete API reference for Rayuela recommendation system",url:t.url||`${s}/docs`,programmingLanguage:["Python","JavaScript","PHP"],...t};default:return r}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23469:(e,t,r)=>{"use strict";r.d(t,{Button:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\button.tsx","Button");(0,s.registerClientReference)(function(){throw Error("Attempted to call buttonVariants() from the server but buttonVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\components\\ui\\button.tsx","buttonVariants")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43315:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,23469))},45805:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},46957:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},47244:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b,metadata:()=>y});var s=r(37413),a=r(78963),i=r(23469),o=r(4536),n=r.n(o),d=r(14350),c=r(45805),l=r(46957),u=r(26373);let p=(0,u.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var h=r(72845),m=r(69117);let x=(0,u.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),g=(0,u.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),y=(0,d.X)({title:"Documentaci\xf3n API",description:"Documentaci\xf3n completa de la API de Rayuela. Gu\xedas de inicio r\xe1pido, referencias de API, ejemplos de c\xf3digo y mejores pr\xe1cticas.",path:"/docs",keywords:["documentaci\xf3n","API","gu\xeda","tutorial","referencia","SDK","ejemplos"]}),f=[{icon:c.A,title:"Inicio R\xe1pido",description:"Comienza a usar Rayuela en minutos",links:[{title:"Python",href:"/docs/quickstart/python"},{title:"JavaScript/Node.js",href:"/docs/quickstart/nodejs"},{title:"PHP",href:"/docs/quickstart/php"}]},{icon:l.A,title:"Referencia de API",description:"Documentaci\xf3n completa de todos los endpoints",links:[{title:"Autenticaci\xf3n",href:"/docs/api/authentication"},{title:"Recomendaciones",href:"/docs/api/recommendations"},{title:"Pipeline de Entrenamiento",href:"/docs/api/pipeline"}]},{icon:p,title:"Ingesta de Datos",description:"C\xf3mo enviar datos de productos, usuarios e interacciones",links:[{title:"Gu\xeda de Ingesta",href:"/docs/guides/data-ingestion"},{title:"Formatos de Datos",href:"/docs/guides/data-formats"},{title:"Mejores Pr\xe1cticas",href:"/docs/guides/best-practices"}]},{icon:h.A,title:"Analytics y M\xe9tricas",description:"Monitorea el rendimiento de tus recomendaciones",links:[{title:"M\xe9tricas Disponibles",href:"/docs/analytics/metrics"},{title:"Dashboards",href:"/docs/analytics/dashboards"},{title:"Reportes",href:"/docs/analytics/reports"}]},{icon:m.A,title:"Seguridad",description:"Autenticaci\xf3n, autorizaci\xf3n y mejores pr\xe1cticas",links:[{title:"API Keys",href:"/docs/security/api-keys"},{title:"JWT Tokens",href:"/docs/security/jwt"},{title:"Rate Limiting",href:"/docs/security/rate-limiting"}]},{icon:x,title:"Gu\xedas Avanzadas",description:"Casos de uso espec\xedficos y configuraciones avanzadas",links:[{title:"Cold Start",href:"/docs/guides/cold-start"},{title:"A/B Testing",href:"/docs/guides/ab-testing"},{title:"Personalizaci\xf3n",href:"/docs/guides/personalization"}]}];function b(){let e=(0,d.u)("APIReference",{name:"Rayuela API Documentation",description:"Complete API reference for Rayuela recommendation system",url:"https://rayuela.ai/docs"});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(e)}}),(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-marketing-gradient-start to-marketing-gradient-end",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6",children:"Documentaci\xf3n de Rayuela"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8",children:"Todo lo que necesitas para integrar sistemas de recomendaci\xf3n en tu aplicaci\xf3n. Desde gu\xedas de inicio r\xe1pido hasta referencias detalladas de API."}),(0,s.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,s.jsx)(i.Button,{asChild:!0,size:"lg",children:(0,s.jsxs)(n(),{href:"/docs/quickstart/python",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),"Inicio R\xe1pido"]})}),(0,s.jsx)(i.Button,{variant:"outline",size:"lg",asChild:!0,children:(0,s.jsxs)(n(),{href:"/docs/api/recommendations",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 mr-2"}),"Referencia API"]})})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16",children:f.map((e,t)=>{let r=e.icon;return(0,s.jsxs)(a.Zp,{className:"h-full",children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4",children:(0,s.jsx)(r,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),(0,s.jsx)(a.ZB,{className:"text-xl",children:e.title}),(0,s.jsx)(a.BT,{children:e.description})]}),(0,s.jsx)(a.Wu,{children:(0,s.jsx)("ul",{className:"space-y-2",children:e.links.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsxs)(n(),{href:e.href,className:"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors",children:[(0,s.jsx)(g,{className:"w-4 h-4 mr-2"}),e.title]})},t))})})]},t)})}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-center text-gray-900 dark:text-white mb-8",children:"Gu\xedas Populares"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)(n(),{href:"/docs/quickstart/python",className:"p-6 border rounded-lg hover:shadow-md transition-shadow",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"\uD83D\uDC0D Inicio R\xe1pido con Python"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Integra Rayuela en tu aplicaci\xf3n Python en menos de 5 minutos."})]}),(0,s.jsxs)(n(),{href:"/docs/guides/data-ingestion",className:"p-6 border rounded-lg hover:shadow-md transition-shadow",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"\uD83D\uDCCA Gu\xeda de Ingesta de Datos"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Aprende a enviar datos de productos, usuarios e interacciones."})]}),(0,s.jsxs)(n(),{href:"/docs/api/recommendations",className:"p-6 border rounded-lg hover:shadow-md transition-shadow",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"\uD83C\uDFAF API de Recomendaciones"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Referencia completa para obtener recomendaciones personalizadas."})]}),(0,s.jsxs)(n(),{href:"/docs/guides/cold-start",className:"p-6 border rounded-lg hover:shadow-md transition-shadow",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"\uD83D\uDE80 Manejo de Cold Start"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Estrategias para nuevos usuarios y productos sin historial."})]})]})]})]})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(37413);function a({children:e}){return(0,s.jsx)("div",{children:(0,s.jsx)("main",{children:e})})}r(61120)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63819:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let c={children:["",{children:["(public)",{children:["docs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,47244)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58448)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["D:\\vscode_workspace\\cloned_repos\\rayuela\\rayuela_frontend\\src\\app\\(public)\\docs\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(public)/docs/page",pathname:"/docs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},69117:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},72845:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78963:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>o});var s=r(37413);r(61120);var a=r(10974);function i({className:e,elevation:t="soft",...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-lg border",{none:"shadow-none",sm:"shadow-sm",soft:"shadow-soft",medium:"shadow-medium",glow:"shadow-glow"}[t]??"shadow-soft","rayuela-card-gradient rayuela-card-hover","transition-all duration-300 ease-in-out",e),...r})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("text-subheading rayuela-accent",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-caption",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2713,5814,5836,2807],()=>r(63819));module.exports=s})();